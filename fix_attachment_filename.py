#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复物料附件文件名显示问题
解决文件名显示为扩展名的问题
"""

import mysql.connector
import sys
import os
import re
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

def get_db_connection():
    """建立数据库连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def analyze_attachment_filenames():
    """分析附件文件名问题"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 正在分析附件文件名...")
        
        # 查询所有附件
        cursor.execute("""
            SELECT id, material_id, file_name, file_path, file_extension, upload_time
            FROM material_attachments 
            WHERE is_active = TRUE
            ORDER BY upload_time DESC
        """)
        
        attachments = cursor.fetchall()
        
        if not attachments:
            print("📝 没有找到附件记录")
            return []
        
        print(f"📊 找到 {len(attachments)} 个附件记录")
        print("\n" + "="*80)
        
        problematic_attachments = []
        
        for attachment in attachments:
            file_name = attachment['file_name']
            file_extension = attachment['file_extension']
            file_path = attachment['file_path']
            
            # 检查问题类型
            issues = []
            
            # 1. 文件名为空或只有扩展名
            if not file_name or file_name.strip() == '':
                issues.append("文件名为空")
            elif file_name == file_extension:
                issues.append(f"文件名只有扩展名: {file_extension}")
            elif file_name.startswith('.'):
                issues.append("文件名以点开头")
            
            # 2. 文件名不包含扩展名但应该包含
            if file_name and not file_name.endswith(f'.{file_extension}') and file_extension:
                issues.append("文件名缺少扩展名")
            
            # 3. 从文件路径中提取真实文件名
            real_filename = None
            if file_path:
                path_parts = file_path.replace('\\', '/').split('/')
                if path_parts:
                    stored_filename = path_parts[-1]
                    # 移除UUID前缀，获取原始文件名
                    if '_' in stored_filename:
                        uuid_part, original_part = stored_filename.split('_', 1)
                        if len(uuid_part) == 32:  # UUID长度
                            real_filename = original_part
            
            if issues or real_filename:
                problematic_attachments.append({
                    'id': attachment['id'],
                    'material_id': attachment['material_id'],
                    'current_name': file_name,
                    'file_extension': file_extension,
                    'file_path': file_path,
                    'real_filename': real_filename,
                    'issues': issues,
                    'upload_time': attachment['upload_time']
                })
                
                print(f"📄 附件ID: {attachment['id']}")
                print(f"   当前文件名: '{file_name}'")
                print(f"   文件扩展名: '{file_extension}'")
                print(f"   真实文件名: '{real_filename}'")
                print(f"   问题: {', '.join(issues) if issues else '需要验证'}")
                print(f"   上传时间: {attachment['upload_time']}")
                print("-" * 40)
        
        print(f"\n📊 分析完成，发现 {len(problematic_attachments)} 个可能有问题的附件")
        return problematic_attachments
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def fix_attachment_filenames(problematic_attachments, auto_fix=False):
    """修复附件文件名"""
    if not problematic_attachments:
        print("✅ 没有需要修复的附件")
        return True
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print(f"\n🔧 开始修复 {len(problematic_attachments)} 个附件的文件名...")
        
        fixed_count = 0
        
        for attachment in problematic_attachments:
            attachment_id = attachment['id']
            current_name = attachment['current_name']
            file_extension = attachment['file_extension']
            real_filename = attachment['real_filename']
            issues = attachment['issues']
            
            # 确定新的文件名
            new_filename = None
            
            if real_filename and real_filename != current_name:
                # 使用从文件路径提取的真实文件名
                new_filename = real_filename
            elif current_name == file_extension and file_extension:
                # 如果当前文件名只是扩展名，生成一个有意义的文件名
                timestamp = attachment['upload_time'].strftime('%Y%m%d_%H%M%S')
                new_filename = f"附件_{timestamp}.{file_extension}"
            elif not current_name or current_name.strip() == '':
                # 如果文件名为空，生成一个文件名
                timestamp = attachment['upload_time'].strftime('%Y%m%d_%H%M%S')
                new_filename = f"附件_{timestamp}.{file_extension}" if file_extension else f"附件_{timestamp}"
            elif not current_name.endswith(f'.{file_extension}') and file_extension:
                # 如果文件名缺少扩展名，添加扩展名
                new_filename = f"{current_name}.{file_extension}"
            
            if new_filename and new_filename != current_name:
                if not auto_fix:
                    print(f"\n📄 附件ID: {attachment_id}")
                    print(f"   当前文件名: '{current_name}'")
                    print(f"   建议文件名: '{new_filename}'")
                    print(f"   问题: {', '.join(issues)}")
                    
                    choice = input("   是否修复此附件? (y/n/a=全部/q=退出): ").lower().strip()
                    if choice == 'q':
                        break
                    elif choice == 'a':
                        auto_fix = True
                    elif choice != 'y' and choice != 'a':
                        continue
                
                # 执行修复
                cursor.execute("""
                    UPDATE material_attachments 
                    SET file_name = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (new_filename, attachment_id))
                
                fixed_count += 1
                print(f"✅ 已修复附件 {attachment_id}: '{current_name}' -> '{new_filename}'")
        
        if fixed_count > 0:
            conn.commit()
            print(f"\n🎉 修复完成！共修复了 {fixed_count} 个附件的文件名")
        else:
            print("\n📝 没有执行任何修复操作")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 物料附件文件名修复工具")
    print("=" * 50)
    
    # 1. 分析问题
    problematic_attachments = analyze_attachment_filenames()
    
    if not problematic_attachments:
        print("✅ 所有附件文件名都正常，无需修复")
        return
    
    # 2. 询问是否修复
    print(f"\n发现 {len(problematic_attachments)} 个可能有问题的附件")
    choice = input("是否开始修复? (y/n): ").lower().strip()
    
    if choice == 'y':
        # 3. 执行修复
        fix_attachment_filenames(problematic_attachments)
    else:
        print("❌ 用户取消修复操作")

if __name__ == "__main__":
    main()
