# QMS文件管理器部署指南

## 📋 概述

QMS文件管理器是专为解决局域网用户文件下载问题而设计的企业级解决方案。通过本地应用程序配合浏览器扩展，实现真正的指定路径下载和自动文件打开功能。

## 🎯 解决的问题

### 原有问题
- ❌ 文件只能下载到浏览器默认文件夹
- ❌ 下载后需要手动查找和打开文件
- ❌ 文件分散存储，难以管理
- ❌ 影响工作效率

### 解决方案
- ✅ 文件直接下载到指定工作文件夹（如 C:\QMS1\）
- ✅ 下载完成后自动打开文件
- ✅ 统一的文件存储和管理
- ✅ 大幅提升工作效率

## 🏗️ 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   QMS网页   │───▶│ 浏览器扩展  │───▶│ 本地应用    │
│             │    │             │    │             │
│ 点击"打开"  │    │ 拦截请求    │    │ 下载文件    │
│ 附件按钮    │    │ 发送到本地  │    │ 自动打开    │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 📦 组件说明

### 1. 本地客户端应用
- **技术栈**: Python + tkinter
- **功能**: 文件下载、管理、自动打开
- **服务**: HTTP服务器（端口8765）
- **界面**: 图形化配置和状态监控

### 2. 浏览器扩展
- **平台**: Chrome/Edge
- **功能**: 拦截下载请求，与本地应用通信
- **界面**: 状态指示器和设置面板

### 3. 网页端集成
- **功能**: 自动检测扩展，智能降级
- **兼容**: 保持原有功能不变

## 🚀 部署步骤

### 准备工作
- **操作系统**: Windows 10/11
- **Python**: 3.7+ (如未安装需先安装)
- **浏览器**: Chrome 88+ 或 Edge 88+
- **权限**: 管理员权限（首次安装）

### 第一步：部署本地应用

1. **下载文件包**
   ```
   解压 QMSFileManager.zip 到本地
   推荐路径：C:\Program Files\QMSFileManager\
   ```

2. **运行安装程序**
   ```
   右键以管理员身份运行 install.bat
   等待安装完成
   ```

3. **启动应用**
   ```
   双击桌面快捷方式"QMS文件管理器"
   或运行 start_qms_manager.bat
   ```

4. **配置设置**
   ```
   下载路径：C:\QMS1\ (可自定义)
   自动打开：启用
   QMS服务器：**************:5000
   点击"保存设置"
   ```

### 第二步：安装浏览器扩展

1. **打开扩展管理页面**
   ```
   Chrome: 地址栏输入 chrome://extensions/
   Edge: 地址栏输入 edge://extensions/
   ```

2. **启用开发者模式**
   ```
   右上角开启"开发者模式"开关
   ```

3. **加载扩展**
   ```
   点击"加载已解压的扩展程序"
   选择文件夹：QMSFileManager\browser_extension
   确认扩展已启用
   ```

4. **验证安装**
   ```
   扩展栏应显示QMS文件管理器图标
   点击图标查看状态
   ```

### 第三步：测试功能

1. **检查连接状态**
   ```
   本地应用显示："就绪 - 等待下载请求"
   浏览器扩展显示："已连接"
   ```

2. **测试下载功能**
   ```
   访问QMS系统
   点击任意附件的"打开"按钮
   确认文件下载到指定路径并自动打开
   ```

## 🎮 使用指南

### 日常使用流程

1. **启动准备**
   - 开机后启动QMS文件管理器
   - 确认状态为"就绪"

2. **访问QMS**
   - 打开Chrome浏览器
   - 访问QMS系统
   - 确认右上角显示"QMS文件管理器(已连接)"

3. **下载文件**
   - 点击附件"打开"按钮
   - 系统自动处理：
     - PDF/图片 → 浏览器预览
     - Office/CAD → 下载并打开

4. **文件管理**
   - 所有文件统一保存在C:\QMS1\
   - 可在本地应用中查看下载历史
   - 支持一键打开下载文件夹

### 高级功能

#### 自定义下载路径
```
1. 在本地应用中点击"浏览"
2. 选择新的下载文件夹
3. 点击"保存设置"
4. 重启应用生效
```

#### 扩展设置
```
1. 点击浏览器扩展图标
2. 查看连接状态
3. 修改本地应用地址（如有需要）
4. 测试连接功能
```

## 🔧 故障排除

### 常见问题

#### 1. 扩展显示"未连接"
**症状**: 浏览器扩展状态为红色"未连接"
**原因**: 本地应用未启动或端口被占用
**解决方案**:
```
1. 确认QMS文件管理器应用正在运行
2. 检查任务管理器中是否有Python进程
3. 重启本地应用
4. 检查防火墙是否阻止端口8765
```

#### 2. 下载失败
**症状**: 点击"打开"后没有反应或报错
**原因**: 网络问题或权限问题
**解决方案**:
```
1. 检查网络连接到QMS服务器
2. 确认下载路径有写入权限
3. 尝试手动创建下载文件夹
4. 查看本地应用的错误信息
```

#### 3. 文件无法自动打开
**症状**: 文件下载成功但未自动打开
**原因**: 缺少对应的程序或文件关联
**解决方案**:
```
1. 安装对应软件（Office、PDF阅读器等）
2. 检查文件关联设置
3. 手动打开文件测试
4. 在本地应用中关闭"自动打开"选项
```

#### 4. 端口冲突
**症状**: 本地应用启动失败，提示端口被占用
**原因**: 端口8765被其他程序使用
**解决方案**:
```
1. 关闭占用端口的程序
2. 或修改配置文件中的端口号
3. 重启本地应用
4. 更新扩展中的本地应用地址
```

### 日志查看

#### 本地应用日志
```
位置：应用程序界面的状态区域
内容：连接状态、下载进度、错误信息
```

#### 浏览器日志
```
1. 按F12打开开发者工具
2. 切换到Console标签
3. 查找QMS相关的日志信息
```

#### 扩展日志
```
1. 访问 chrome://extensions/
2. 找到QMS文件管理器扩展
3. 点击"详细信息"
4. 点击"检查视图"查看日志
```

## 📞 技术支持

### 联系方式
- **内部支持**: 品质中心IT部门
- **问题反馈**: 通过QMS系统提交工单
- **紧急情况**: 技术支持热线

### 常见支持请求
1. **安装协助**: 远程协助安装和配置
2. **问题诊断**: 分析和解决技术问题
3. **功能培训**: 用户使用培训
4. **版本更新**: 软件版本升级

## 📊 效果对比

| 项目 | 原方案 | 新方案 | 改进效果 |
|------|--------|--------|----------|
| 下载位置 | 浏览器默认文件夹 | 指定工作文件夹 | ⭐⭐⭐⭐⭐ |
| 文件打开 | 手动查找打开 | 自动打开 | ⭐⭐⭐⭐⭐ |
| 文件管理 | 分散存储 | 统一管理 | ⭐⭐⭐⭐⭐ |
| 工作效率 | 多步操作 | 一键完成 | ⭐⭐⭐⭐⭐ |
| 用户体验 | 一般 | 优秀 | ⭐⭐⭐⭐⭐ |

## 🔒 安全说明

### 数据安全
- ✅ 所有文件处理在本地进行
- ✅ 不上传文件到第三方服务器
- ✅ 用户数据仅存储在本地

### 网络安全
- ✅ 仅与指定QMS服务器通信
- ✅ 使用HTTPS加密传输
- ✅ 最小权限原则

### 系统安全
- ✅ 不修改系统关键文件
- ✅ 可完全卸载
- ✅ 不影响其他软件

## 📈 维护建议

### 定期维护
- **每月**: 检查软件运行状态
- **每季度**: 清理下载历史和临时文件
- **每半年**: 检查软件更新

### 备份建议
- **配置备份**: 定期备份用户配置
- **文件备份**: 定期备份下载文件夹
- **系统备份**: 包含在系统整体备份中

---

**QMS文件管理器** - 让文件下载更智能，让工作更高效！

如有任何问题，请联系技术支持团队。
