# 批量导入待检功能说明

## 📋 功能概述

批量导入待检功能是为QMS质量管理系统新增的功能模块，允许用户提前录入待检物料信息，建立待检清单，然后对单个物料进行编辑检验。该功能分别在抽样检验和全部检验模块中提供。

## 🎯 功能目标

### 主要目标
- **提前规划**：可以提前把需要待检的项目先输入料号
- **信息自动填充**：自动获取物料基础信息（名称、规格、供应商）
- **批量管理**：支持批量录入和管理待检物料
- **流程优化**：从待检清单直接创建检验记录

### 业务价值
- **提高效率**：减少重复录入，提高检验准备效率
- **规范管理**：统一的待检物料管理流程
- **信息准确**：自动获取物料信息，减少人工错误
- **流程可视**：清晰的待检状态管理

## 🏗️ 系统架构

### 数据库设计

#### 1. 待检物料表 (pending_inspections)
```sql
CREATE TABLE pending_inspections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL,           -- 物料料号
    material_name VARCHAR(200),                   -- 物料名称
    specification VARCHAR(500),                   -- 规格型号
    supplier_name VARCHAR(200),                   -- 供应商名称
    incoming_quantity DECIMAL(10,3),              -- 来料数量
    unit VARCHAR(20),                             -- 单位
    inspection_type ENUM('sampling', 'full'),     -- 检验类型
    status ENUM('pending', 'in_progress', 'completed', 'cancelled'), -- 状态
    batch_number VARCHAR(100),                    -- 批次号
    arrival_date DATE,                            -- 到货日期
    planned_inspection_date DATE,                 -- 计划检验日期
    inspector VARCHAR(50),                        -- 检验员
    remarks TEXT,                                 -- 备注
    batch_id INT,                                 -- 批次ID
    inspection_record_id INT,                     -- 关联的检验记录ID
    created_by VARCHAR(50),                       -- 创建人
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),                       -- 更新人
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 待检物料批次表 (pending_inspection_batches)
```sql
CREATE TABLE pending_inspection_batches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    batch_name VARCHAR(100) NOT NULL,             -- 批次名称
    inspection_type ENUM('sampling', 'full'),     -- 检验类型
    total_items INT DEFAULT 0,                    -- 总物料数
    pending_items INT DEFAULT 0,                  -- 待检数量
    in_progress_items INT DEFAULT 0,              -- 检验中数量
    completed_items INT DEFAULT 0,                -- 已完成数量
    created_by VARCHAR(50),                       -- 创建人
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### API接口设计

#### 1. 获取待检物料列表
```
GET /pending_inspection/api/pending_inspections
参数：
- type: 检验类型 (sampling/full)
- status: 状态筛选
- page: 页码
- per_page: 每页数量
```

#### 2. 批量导入待检物料
```
POST /pending_inspection/api/pending_inspections/batch_import
请求体：
{
    "inspection_type": "sampling",
    "batch_name": "批次名称",
    "materials": [
        {
            "material_code": "料号",
            "incoming_quantity": 100,
            "batch_number": "批次号",
            "arrival_date": "2024-01-01"
        }
    ]
}
```

#### 3. 开始检验
```
POST /pending_inspection/api/pending_inspections/{id}/start_inspection
功能：将待检物料转换为检验记录
```

## 🎨 用户界面设计

### 1. 导航栏集成
在抽样检验和全部检验模块的导航栏中，在"新增检验"右侧添加"批量导入待检"链接。

### 2. 批量导入页面
- **导入方式选择**：手动录入 / 文件导入
- **手动录入表格**：支持逐行输入物料信息
- **文件导入区域**：支持Excel文件拖拽上传
- **数据预览**：导入前预览数据和验证
- **批量操作**：添加行、删除行、清空数据

### 3. 待检清单页面
- **筛选功能**：按状态、物料料号、供应商、日期筛选
- **数据统计**：显示各状态的物料数量
- **操作按钮**：开始检验、编辑、删除
- **分页显示**：支持大量数据的分页浏览

## 🔄 业务流程

### 1. 批量导入流程
```
选择导入方式 → 录入物料信息 → 自动获取基础信息 → 预览数据 → 确认导入 → 生成待检清单
```

### 2. 检验执行流程
```
待检清单 → 选择物料 → 开始检验 → 创建检验记录 → 执行检验 → 完成检验 → 更新状态
```

### 3. 状态管理流程
```
pending (待检) → in_progress (检验中) → completed (已完成)
                                    → cancelled (已取消)
```

## 🔧 核心功能详解

### 1. 智能信息获取
当用户输入物料料号时，系统自动：
- **获取物料基础信息**：从materials表获取名称、规格、单位
- **获取最近供应商**：优先级顺序
  1. 最近一次抽样检验记录的供应商
  2. 最近一次全部检验记录的供应商
  3. 物料信息中的默认供应商

### 2. 批量导入验证
- **必填字段检查**：物料料号不能为空
- **数据格式验证**：数量、日期格式验证
- **重复性检查**：同一批次内料号重复检查
- **物料存在性验证**：验证料号是否在系统中存在

### 3. 状态自动管理
- **创建时**：状态为 pending
- **开始检验**：状态变为 in_progress，创建检验记录
- **完成检验**：状态变为 completed
- **取消检验**：状态变为 cancelled

## 📱 页面功能说明

### 批量导入页面功能

#### 手动录入模式
- **动态表格**：支持添加/删除行
- **智能填充**：输入料号后自动获取物料信息
- **实时验证**：输入时进行格式验证
- **批量操作**：支持清空所有数据

#### 文件导入模式
- **拖拽上传**：支持文件拖拽到指定区域
- **格式限制**：仅支持.xlsx和.xls格式
- **模板下载**：提供标准导入模板
- **数据解析**：自动解析Excel文件内容

#### 数据预览功能
- **数据展示**：表格形式展示所有待导入数据
- **状态标识**：显示每行数据的验证状态
- **错误提示**：高亮显示有问题的数据
- **统计信息**：显示总数、成功数、错误数

### 待检清单页面功能

#### 筛选和搜索
- **多维度筛选**：状态、料号、供应商、日期
- **实时搜索**：输入关键词实时筛选
- **筛选重置**：一键清空所有筛选条件
- **筛选记忆**：保持用户的筛选偏好

#### 数据展示
- **分页显示**：支持大量数据的分页浏览
- **排序功能**：支持按各列排序
- **状态标识**：不同颜色标识不同状态
- **操作按钮**：根据状态显示不同操作

#### 批量操作
- **批量选择**：支持多选物料
- **批量开始检验**：一次性开始多个物料的检验
- **批量删除**：删除多个待检物料
- **批量导出**：导出待检清单

## 🔗 系统集成

### 与现有模块的集成

#### 1. 物料管理模块
- **物料信息获取**：从materials表获取基础信息
- **供应商信息**：获取物料的供应商信息
- **单位信息**：获取物料的计量单位

#### 2. 检验记录模块
- **记录创建**：从待检物料创建检验记录
- **数据传递**：将待检信息传递给检验记录
- **状态同步**：检验完成后同步状态

#### 3. 用户权限模块
- **操作权限**：检查用户的操作权限
- **数据权限**：控制用户可见的数据范围
- **审核权限**：检验记录的审核权限

## 📊 数据流转

### 1. 导入阶段
```
用户输入 → 数据验证 → 信息补全 → 批次创建 → 数据存储
```

### 2. 管理阶段
```
待检清单 → 筛选查询 → 状态管理 → 信息编辑 → 数据更新
```

### 3. 执行阶段
```
选择物料 → 创建记录 → 状态更新 → 检验执行 → 结果记录
```

## 🎯 使用场景

### 场景1：日常来料检验准备
1. **计划阶段**：根据生产计划，提前录入本周需要检验的物料
2. **准备阶段**：检验员查看待检清单，准备检验设备和标准
3. **执行阶段**：物料到货后，直接从待检清单开始检验

### 场景2：批量物料到货
1. **批量录入**：使用Excel模板批量导入多个物料信息
2. **信息核对**：系统自动填充物料信息，人工核对供应商和数量
3. **分批检验**：根据检验能力，分批开始检验

### 场景3：供应商来料管理
1. **供应商通知**：供应商提前通知发货清单
2. **预先录入**：根据发货清单预先录入待检信息
3. **到货检验**：物料到货后立即开始检验，提高效率

## 🔧 技术实现要点

### 1. 前端技术
- **响应式设计**：适配不同屏幕尺寸
- **异步加载**：使用Ajax实现数据的异步加载
- **实时验证**：前端实时验证用户输入
- **用户体验**：友好的交互提示和加载状态

### 2. 后端技术
- **数据验证**：服务端数据验证和清洗
- **事务处理**：确保数据一致性
- **错误处理**：完善的错误处理机制
- **性能优化**：数据库查询优化

### 3. 数据库设计
- **索引优化**：关键字段建立索引
- **外键约束**：保证数据完整性
- **状态管理**：清晰的状态转换逻辑
- **历史记录**：保留操作历史

## 📈 预期效果

### 1. 效率提升
- **录入效率**：批量导入比逐个录入效率提升80%
- **准备时间**：检验准备时间减少50%
- **查找时间**：通过待检清单快速定位物料

### 2. 质量改善
- **信息准确性**：自动获取信息，减少人工错误
- **流程规范性**：统一的待检管理流程
- **追溯完整性**：完整的操作记录和状态跟踪

### 3. 管理优化
- **计划性**：提前规划检验工作
- **可视化**：清晰的待检状态展示
- **统计分析**：支持检验工作量统计

---

**批量导入待检功能** - 让检验准备更高效，让质量管理更规范！
