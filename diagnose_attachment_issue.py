#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
附件文件名显示问题诊断脚本
用于检查和修复附件文件名显示问题
"""

import sys
import os
import re
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config import get_db_connection

def diagnose_attachment_filenames():
    """诊断附件文件名问题"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 正在诊断附件文件名问题...")
        print("=" * 60)
        
        # 查询所有活跃的附件
        cursor.execute("""
            SELECT 
                id, 
                material_id, 
                file_name, 
                file_path, 
                file_extension, 
                file_size,
                upload_time,
                description
            FROM material_attachments 
            WHERE is_active = TRUE
            ORDER BY upload_time DESC
        """)
        
        attachments = cursor.fetchall()
        
        if not attachments:
            print("📝 没有找到附件记录")
            return
        
        print(f"📊 找到 {len(attachments)} 个附件记录\n")
        
        # 分析每个附件
        problematic_count = 0
        
        for i, attachment in enumerate(attachments, 1):
            attachment_id = attachment['id']
            material_id = attachment['material_id']
            file_name = attachment['file_name'] or ''
            file_path = attachment['file_path'] or ''
            file_extension = attachment['file_extension'] or ''
            file_size = attachment['file_size']
            upload_time = attachment['upload_time']
            description = attachment['description'] or ''
            
            print(f"📄 附件 #{i} (ID: {attachment_id})")
            print(f"   物料ID: {material_id}")
            print(f"   文件名: '{file_name}'")
            print(f"   文件路径: '{file_path}'")
            print(f"   文件扩展名: '{file_extension}'")
            print(f"   文件大小: {file_size} bytes")
            print(f"   上传时间: {upload_time}")
            print(f"   描述: '{description}'")
            
            # 检查问题
            issues = []
            
            # 1. 检查文件名是否为空或只有扩展名
            if not file_name or file_name.strip() == '':
                issues.append("文件名为空")
            elif file_name == file_extension:
                issues.append("文件名只有扩展名")
            elif len(file_name) < 3:
                issues.append("文件名过短")
            
            # 2. 从文件路径提取真实文件名
            real_filename = None
            if file_path:
                # 从路径中提取文件名
                path_filename = os.path.basename(file_path)
                if path_filename and '_' in path_filename:
                    # 移除UUID前缀
                    parts = path_filename.split('_', 1)
                    if len(parts) > 1:
                        real_filename = parts[1]
                        print(f"   从路径提取的文件名: '{real_filename}'")
            
            # 3. 检查文件是否存在
            file_exists = False
            if file_path:
                # 构建完整路径
                base_dir = os.path.dirname(os.path.abspath(__file__))
                if os.path.isabs(file_path):
                    full_path = file_path
                else:
                    full_path = os.path.join(base_dir, file_path)
                
                file_exists = os.path.exists(full_path)
                print(f"   文件存在: {'是' if file_exists else '否'}")
                if not file_exists:
                    issues.append("物理文件不存在")
            
            if issues:
                problematic_count += 1
                print(f"   ❌ 问题: {', '.join(issues)}")
                
                # 建议修复方案
                if real_filename and real_filename != file_name:
                    print(f"   💡 建议文件名: '{real_filename}'")
                elif file_name == file_extension and file_extension:
                    timestamp = upload_time.strftime('%Y%m%d_%H%M%S') if upload_time else datetime.now().strftime('%Y%m%d_%H%M%S')
                    suggested_name = f"附件_{timestamp}.{file_extension}"
                    print(f"   💡 建议文件名: '{suggested_name}'")
            else:
                print(f"   ✅ 文件名正常")
            
            print("-" * 40)
        
        print(f"\n📊 诊断完成:")
        print(f"   总附件数: {len(attachments)}")
        print(f"   有问题的附件: {problematic_count}")
        print(f"   正常附件: {len(attachments) - problematic_count}")
        
        if problematic_count > 0:
            print(f"\n🔧 发现 {problematic_count} 个附件需要修复")
            return True
        else:
            print(f"\n✅ 所有附件都正常")
            return False
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def fix_attachment_filenames():
    """修复附件文件名"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("\n🔧 开始修复附件文件名...")
        
        # 查找有问题的附件
        cursor.execute("""
            SELECT 
                id, 
                material_id, 
                file_name, 
                file_path, 
                file_extension, 
                upload_time
            FROM material_attachments 
            WHERE is_active = TRUE
            AND (
                file_name = '' 
                OR file_name IS NULL 
                OR file_name = file_extension 
                OR LENGTH(file_name) < 3
            )
            ORDER BY upload_time DESC
        """)
        
        problematic_attachments = cursor.fetchall()
        
        if not problematic_attachments:
            print("✅ 没有需要修复的附件")
            return True
        
        print(f"🔍 找到 {len(problematic_attachments)} 个需要修复的附件")
        
        fixed_count = 0
        
        for attachment in problematic_attachments:
            attachment_id = attachment['id']
            file_name = attachment['file_name'] or ''
            file_path = attachment['file_path'] or ''
            file_extension = attachment['file_extension'] or ''
            upload_time = attachment['upload_time']
            
            # 确定新文件名
            new_filename = None
            
            # 1. 尝试从文件路径提取真实文件名
            if file_path:
                path_filename = os.path.basename(file_path)
                if path_filename and '_' in path_filename:
                    parts = path_filename.split('_', 1)
                    if len(parts) > 1:
                        new_filename = parts[1]
            
            # 2. 如果无法从路径提取，生成一个有意义的文件名
            if not new_filename:
                timestamp = upload_time.strftime('%Y%m%d_%H%M%S') if upload_time else datetime.now().strftime('%Y%m%d_%H%M%S')
                if file_extension:
                    new_filename = f"附件_{timestamp}.{file_extension}"
                else:
                    new_filename = f"附件_{timestamp}"
            
            # 3. 执行更新
            if new_filename and new_filename != file_name:
                cursor.execute("""
                    UPDATE material_attachments 
                    SET file_name = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (new_filename, attachment_id))
                
                fixed_count += 1
                print(f"✅ 修复附件 {attachment_id}: '{file_name}' -> '{new_filename}'")
        
        if fixed_count > 0:
            conn.commit()
            print(f"\n🎉 修复完成！共修复了 {fixed_count} 个附件的文件名")
        else:
            print("\n📝 没有执行任何修复操作")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 附件文件名问题诊断和修复工具")
    print("=" * 60)
    
    # 1. 诊断问题
    has_issues = diagnose_attachment_filenames()
    
    if has_issues:
        # 2. 询问是否修复
        print("\n" + "=" * 60)
        choice = input("是否立即修复这些问题? (y/n): ").lower().strip()
        
        if choice == 'y':
            # 3. 执行修复
            fix_attachment_filenames()
            
            # 4. 再次诊断验证
            print("\n" + "=" * 60)
            print("🔍 验证修复结果...")
            diagnose_attachment_filenames()
        else:
            print("❌ 用户取消修复操作")
    
    print("\n" + "=" * 60)
    print("🏁 诊断完成")

if __name__ == "__main__":
    main()
