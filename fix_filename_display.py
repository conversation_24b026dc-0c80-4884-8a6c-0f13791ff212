#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复附件文件名显示问题的简单脚本
"""

import mysql.connector
import os
import re
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Jianzhi9.',
    'database': 'quality_control'
}

def get_connection():
    """获取数据库连接"""
    return mysql.connector.connect(**DB_CONFIG)

def fix_attachment_filenames():
    """修复附件文件名显示问题"""
    try:
        conn = get_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 查找需要修复的附件...")
        
        # 查找有问题的附件
        cursor.execute("""
            SELECT 
                id, 
                material_id, 
                file_name, 
                file_path, 
                file_extension, 
                upload_time
            FROM material_attachments 
            WHERE is_active = TRUE
            AND (
                file_name = '' 
                OR file_name IS NULL 
                OR file_name = file_extension 
                OR LENGTH(file_name) < 4
            )
            ORDER BY upload_time DESC
        """)
        
        problematic_attachments = cursor.fetchall()
        
        if not problematic_attachments:
            print("✅ 没有发现需要修复的附件")
            return True
        
        print(f"📋 发现 {len(problematic_attachments)} 个需要修复的附件:")
        
        fixed_count = 0
        
        for attachment in problematic_attachments:
            attachment_id = attachment['id']
            current_name = attachment['file_name'] or ''
            file_path = attachment['file_path'] or ''
            file_extension = attachment['file_extension'] or ''
            upload_time = attachment['upload_time']
            
            print(f"\n📄 附件ID: {attachment_id}")
            print(f"   当前文件名: '{current_name}'")
            print(f"   文件路径: '{file_path}'")
            print(f"   扩展名: '{file_extension}'")
            
            # 尝试从文件路径提取真实文件名
            new_filename = None
            
            if file_path:
                # 从路径中提取文件名
                path_filename = os.path.basename(file_path)
                if path_filename and '_' in path_filename:
                    # 移除UUID前缀 (格式: uuid_原始文件名)
                    parts = path_filename.split('_', 1)
                    if len(parts) > 1:
                        extracted_name = parts[1]
                        # 验证提取的文件名是否合理
                        if len(extracted_name) > 3 and '.' in extracted_name:
                            new_filename = extracted_name
                            print(f"   从路径提取: '{new_filename}'")
            
            # 如果无法从路径提取，生成一个有意义的文件名
            if not new_filename:
                timestamp = upload_time.strftime('%Y%m%d_%H%M%S') if upload_time else datetime.now().strftime('%Y%m%d_%H%M%S')
                if file_extension:
                    new_filename = f"附件_{timestamp}.{file_extension}"
                else:
                    new_filename = f"附件_{timestamp}"
                print(f"   生成文件名: '{new_filename}'")
            
            # 执行更新
            if new_filename and new_filename != current_name:
                cursor.execute("""
                    UPDATE material_attachments 
                    SET file_name = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (new_filename, attachment_id))
                
                fixed_count += 1
                print(f"   ✅ 已修复: '{current_name}' -> '{new_filename}'")
            else:
                print(f"   ⚠️  跳过: 无需修复")
        
        if fixed_count > 0:
            conn.commit()
            print(f"\n🎉 修复完成！共修复了 {fixed_count} 个附件的文件名")
            
            # 验证修复结果
            print("\n🔍 验证修复结果...")
            cursor.execute("""
                SELECT id, file_name, file_extension
                FROM material_attachments 
                WHERE is_active = TRUE
                ORDER BY updated_at DESC
                LIMIT 10
            """)
            
            recent_attachments = cursor.fetchall()
            for att in recent_attachments:
                print(f"   ID {att['id']}: '{att['file_name']}' (.{att['file_extension']})")
        else:
            print("\n📝 没有执行任何修复操作")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def test_connection():
    """测试数据库连接"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM material_attachments WHERE is_active = TRUE")
        count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        print(f"✅ 数据库连接成功，找到 {count} 个活跃附件")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 附件文件名显示问题修复工具")
    print("=" * 50)
    
    # 1. 测试数据库连接
    if not test_connection():
        print("请检查数据库配置和连接")
        return
    
    # 2. 执行修复
    print("\n开始修复附件文件名...")
    success = fix_attachment_filenames()
    
    if success:
        print("\n" + "=" * 50)
        print("🏁 修复完成！")
        print("\n📋 建议:")
        print("1. 刷新浏览器页面")
        print("2. 重新查看物料编辑页面的附件列表")
        print("3. 测试上传新文件，验证文件名显示正常")
    else:
        print("\n❌ 修复过程中出现错误")

if __name__ == "__main__":
    main()
