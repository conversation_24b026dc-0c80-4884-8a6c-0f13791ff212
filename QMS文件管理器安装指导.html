<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QMS文件管理器安装指导</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .benefits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .benefit-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #4caf50;
        }

        .benefit-card i {
            font-size: 2em;
            color: #4caf50;
            margin-bottom: 10px;
        }

        .benefit-card h3 {
            margin: 10px 0;
            color: #333;
        }

        .benefit-card p {
            color: #666;
            font-size: 14px;
        }

        .installation-steps {
            margin-bottom: 40px;
        }

        .step {
            display: flex;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #2196f3;
        }

        .step-number {
            background: #2196f3;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .step-content h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .step-content p {
            margin: 0;
            color: #666;
        }

        .step-content .code {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 3px solid #6c757d;
        }

        .download-section {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }

        .download-btn {
            background: white;
            color: #4caf50;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .support-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #2196f3;
        }

        .support-section h3 {
            color: #1565c0;
            margin-top: 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .comparison-table .check {
            color: #4caf50;
            font-weight: bold;
        }

        .comparison-table .cross {
            color: #f44336;
            font-weight: bold;
        }

        .warning {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }

        .warning i {
            color: #ff9800;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-download"></i> QMS文件管理器</h1>
            <p>让文件下载更智能，让工作更高效</p>
        </div>

        <div class="content">
            <div class="benefits">
                <div class="benefit-card">
                    <i class="fas fa-folder-open"></i>
                    <h3>指定路径下载</h3>
                    <p>文件直接下载到工作文件夹，无需手动移动</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-magic"></i>
                    <h3>自动打开文件</h3>
                    <p>下载完成后自动使用默认程序打开</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-tachometer-alt"></i>
                    <h3>提升效率</h3>
                    <p>一键下载，立即使用，大幅提升工作效率</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-shield-alt"></i>
                    <h3>安全可靠</h3>
                    <p>本地处理，数据安全，不依赖第三方服务</p>
                </div>
            </div>

            <div class="comparison-table">
                <h2>功能对比</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>传统方式</th>
                            <th>QMS文件管理器</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>下载位置</td>
                            <td class="cross">❌ 浏览器默认文件夹</td>
                            <td class="check">✅ 指定工作文件夹</td>
                        </tr>
                        <tr>
                            <td>文件打开</td>
                            <td class="cross">❌ 手动查找并打开</td>
                            <td class="check">✅ 自动打开</td>
                        </tr>
                        <tr>
                            <td>文件管理</td>
                            <td class="cross">❌ 分散存储</td>
                            <td class="check">✅ 统一管理</td>
                        </tr>
                        <tr>
                            <td>工作效率</td>
                            <td class="cross">❌ 多步操作</td>
                            <td class="check">✅ 一键完成</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="download-section">
                <h2><i class="fas fa-download"></i> 下载安装包</h2>
                <p>选择适合您的安装包开始使用</p>
                <a href="#" class="download-btn" onclick="downloadInstaller()">
                    <i class="fas fa-download"></i> 下载 QMS文件管理器
                </a>
                <a href="#" class="download-btn" onclick="downloadExtension()">
                    <i class="fas fa-puzzle-piece"></i> 下载 浏览器扩展
                </a>
            </div>

            <div class="installation-steps">
                <h2>安装步骤</h2>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>下载并安装本地应用</h3>
                        <p>下载QMSFileManager.zip文件包，解压到本地文件夹</p>
                        <div class="code">
                            推荐路径：C:\Program Files\QMSFileManager\<br>
                            双击运行：install.bat
                        </div>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>安装浏览器扩展</h3>
                        <p>在Chrome浏览器中安装QMS文件管理器扩展</p>
                        <div class="code">
                            1. 打开 chrome://extensions/<br>
                            2. 开启"开发者模式"<br>
                            3. 点击"加载已解压的扩展程序"<br>
                            4. 选择 browser_extension 文件夹
                        </div>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>配置设置</h3>
                        <p>启动应用并配置下载路径</p>
                        <div class="code">
                            下载路径：C:\QMS1\ (可自定义)<br>
                            自动打开：启用<br>
                            点击"保存设置"
                        </div>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>开始使用</h3>
                        <p>访问QMS系统，享受智能下载体验</p>
                        <div class="code">
                            确认状态：QMS文件管理器 (已连接)<br>
                            点击附件"打开"按钮即可自动下载
                        </div>
                    </div>
                </div>
            </div>

            <div class="warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>注意事项：</strong>
                <ul>
                    <li>需要Windows 10/11操作系统</li>
                    <li>需要Python 3.7或更高版本</li>
                    <li>首次安装需要管理员权限</li>
                    <li>确保防火墙允许本地应用运行</li>
                </ul>
            </div>

            <div class="support-section">
                <h3><i class="fas fa-life-ring"></i> 技术支持</h3>
                <p><strong>遇到问题？</strong></p>
                <ul>
                    <li><strong>安装问题：</strong>联系品质中心IT部门</li>
                    <li><strong>使用问题：</strong>查看用户手册或提交工单</li>
                    <li><strong>功能建议：</strong>通过QMS系统反馈</li>
                </ul>
                <p><strong>常见问题：</strong></p>
                <ul>
                    <li>扩展显示"未连接" → 确认本地应用正在运行</li>
                    <li>下载失败 → 检查网络连接和文件权限</li>
                    <li>文件无法打开 → 安装对应的查看程序</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function downloadInstaller() {
            alert('请联系IT部门获取QMS文件管理器安装包');
        }

        function downloadExtension() {
            alert('浏览器扩展包含在主安装包中，请下载完整安装包');
        }

        // 检测当前环境
        function checkEnvironment() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isWindows = userAgent.includes('windows');
            const isChrome = userAgent.includes('chrome');
            
            if (!isWindows) {
                document.querySelector('.warning').innerHTML += 
                    '<li style="color: #f44336;"><strong>当前系统不是Windows，可能无法正常使用</strong></li>';
            }
            
            if (!isChrome) {
                document.querySelector('.warning').innerHTML += 
                    '<li style="color: #f44336;"><strong>建议使用Chrome浏览器以获得最佳体验</strong></li>';
            }
        }

        document.addEventListener('DOMContentLoaded', checkEnvironment);
    </script>
</body>
</html>
