# 附件上传功能修复说明

## 🔍 问题分析

在物料管理页面的附件上传功能中发现了以下问题：

1. **文件名缺失**: 上传的附件显示时文件名为空
2. **文件丢失**: 上传后的文件无法找到或下载失败

## 🛠️ 修复内容

### 1. 文件名处理问题修复

**问题**: `secure_filename()` 函数会移除中文字符，导致中文文件名变成空字符串。

**修复**: 
- 在 `blueprints/material_management/api.py` 中添加了 `safe_filename()` 函数
- 支持中文字符的安全文件名处理
- 保留原始文件名的可读性

```python
def safe_filename(filename):
    """安全的文件名处理，支持中文字符"""
    if not filename:
        return "unnamed_file"
    
    # 移除路径分隔符和危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 如果secure_filename返回空字符串（通常是因为全是非ASCII字符）
    secure_name = secure_filename(filename)
    if not secure_name or secure_name.strip() == '':
        # 保留原始文件名，只替换危险字符
        safe_name = re.sub(r'[^\w\s\-_\.\u4e00-\u9fff]', '_', filename)
        # 移除多余的下划线和空格
        safe_name = re.sub(r'[_\s]+', '_', safe_name).strip('_')
        return safe_name if safe_name else "unnamed_file"
    
    return secure_name
```

### 2. 文件路径问题修复

**问题**: 文件保存和访问时路径不一致，导致文件丢失。

**修复**:
- 文件保存时使用绝对路径确保正确保存
- 数据库中存储相对路径便于访问
- 下载时正确构建完整文件路径

```python
# 文件保存 - 使用绝对路径
base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
upload_dir = os.path.join(base_dir, 'static', 'uploads', 'material_attachments', subfolder)
file_path = os.path.join(upload_dir, unique_filename)
file.save(file_path)

# 数据库存储 - 使用相对路径
relative_path = os.path.join('static', 'uploads', 'material_attachments', subfolder, unique_filename)
```

### 3. 文件下载修复

**问题**: 下载时无法找到文件。

**修复**:
- 正确处理相对路径和绝对路径的转换
- 确保 `send_file()` 使用正确的文件路径

```python
# 构建完整的文件路径
if os.path.isabs(attachment['file_path']):
    full_file_path = attachment['file_path']
else:
    # 如果是相对路径，构建绝对路径
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    full_file_path = os.path.join(base_dir, attachment['file_path'])

# 使用完整路径发送文件
response = send_file(full_file_path, ...)
```

## 🧪 测试和验证

### 1. 运行修复脚本

```bash
# 修复现有的空文件名数据
python fix_attachment_data.py

# 验证修复效果
python verify_fix.py
```

### 2. 测试上传功能

1. 访问物料管理页面
2. 编辑任意物料
3. 上传包含中文名称的文件
4. 验证文件名正确显示
5. 测试文件下载功能

### 3. 检查目录结构

确保以下目录存在且可写：
```
static/uploads/material_attachments/
├── documents/
├── images/
├── drawings/
└── certificates/
```

## 📋 修复文件列表

- `blueprints/material_management/api.py` - 主要修复文件
- `fix_attachment_data.py` - 数据修复脚本
- `verify_fix.py` - 验证脚本
- `test_attachment_fix.py` - 完整测试脚本

## 🔒 安全性改进

1. **文件名安全**: 移除危险字符，防止路径遍历攻击
2. **路径安全**: 使用安全的路径构建方法
3. **文件验证**: 保持原有的文件类型和大小验证

## 💡 使用建议

1. **重启应用**: 修复后重启Flask应用以确保更改生效
2. **测试功能**: 全面测试附件的上传、显示、下载功能
3. **备份数据**: 在生产环境应用前建议备份数据库
4. **监控日志**: 关注应用日志中的文件操作相关错误

## 🚀 后续优化建议

1. **文件预览**: 为图片和PDF文件添加预览功能
2. **批量操作**: 支持批量上传和删除附件
3. **版本控制**: 为附件添加版本管理功能
4. **存储优化**: 考虑使用云存储或CDN优化文件访问速度
