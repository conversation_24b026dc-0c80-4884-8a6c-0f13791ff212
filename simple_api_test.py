#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本（不依赖requests库）
"""

import urllib.request
import urllib.parse
import json

def test_get_api():
    """测试GET API"""
    try:
        url = "http://localhost:5000/pending_inspection/api/pending_inspections?type=sampling"
        
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
            
        print("✅ GET API测试成功")
        print(f"   响应: {data.get('success', False)}")
        if data.get('success'):
            total = data.get('data', {}).get('total', 0)
            print(f"   当前待检物料数量: {total}")
        
        return True
        
    except Exception as e:
        print(f"❌ GET API测试失败: {e}")
        return False

def test_post_api():
    """测试POST API"""
    try:
        url = "http://localhost:5000/pending_inspection/api/pending_inspections/batch_import"
        
        test_data = {
            "inspection_type": "sampling",
            "batch_name": "API测试批次",
            "materials": [
                {
                    "material_code": "API_TEST_001",
                    "incoming_quantity": 100,
                    "batch_number": "BATCH_API_001",
                    "arrival_date": "2024-01-01"
                }
            ]
        }
        
        data = json.dumps(test_data).encode('utf-8')
        
        req = urllib.request.Request(url, data=data)
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode())
        
        print("✅ POST API测试成功")
        print(f"   响应: {result.get('success', False)}")
        if result.get('success'):
            success_count = result.get('data', {}).get('success_count', 0)
            print(f"   成功导入: {success_count} 个物料")
        
        return True
        
    except Exception as e:
        print(f"❌ POST API测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 简单API测试 ===")
    
    print("\n1. 测试获取待检清单API...")
    test_get_api()
    
    print("\n2. 测试批量导入API...")
    test_post_api()
    
    print("\n3. 再次测试获取清单（验证导入结果）...")
    test_get_api()
    
    print("\n🎉 API测试完成！")
