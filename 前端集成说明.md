# QMS文件管理器前端集成说明

## 📋 概述

本文档说明了如何将QMS文件管理器集成到现有的QMS系统前端中，实现智能文件下载功能。

## 🔧 前端修改内容

### 1. 页面头部状态指示器

在页面头部添加了QMS文件管理器状态指示器，实时显示连接状态：

```html
<!-- QMS文件管理器状态指示器 -->
<div class="qms-manager-status" id="qms-manager-status" style="display: none;">
    <div class="status-indicator" id="status-indicator">
        <i class="fas fa-circle" id="status-icon"></i>
        <span id="status-text">检测中...</span>
    </div>
    <button type="button" class="status-btn" onclick="showQMSManagerInfo()">
        <i class="fas fa-info-circle"></i>
    </button>
</div>
```

### 2. 智能下载逻辑

修改了`openAttachment`函数，增加了QMS文件管理器检测和智能降级：

```javascript
function openAttachment(fileName, url, fileExtension) {
    const extension = fileExtension.toLowerCase();
    const inlineTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'txt', 'html', 'htm', 'xml', 'json'];

    if (inlineTypes.includes(extension)) {
        // 预览文件：浏览器中打开
        window.open(url, '_blank', 'noopener,noreferrer');
    } else {
        // 下载文件：检查QMS文件管理器状态
        const qmsManagerStatus = getQMSManagerStatus();
        
        if (qmsManagerStatus.available && qmsManagerStatus.connected) {
            // 使用QMS文件管理器下载
            showQMSManagerDownloadNotification(fileName);
        } else if (qmsManagerStatus.available && !qmsManagerStatus.connected) {
            // 扩展可用但未连接
            showQMSManagerConnectionError();
            downloadToClientDevice(url, fileName, extension);
        } else {
            // 扩展不可用，使用传统方式
            downloadToClientDevice(url, fileName, extension);
        }
    }
}
```

### 3. 状态检测和管理

添加了完整的状态检测和管理系统：

```javascript
// QMS文件管理器状态管理
let qmsManagerStatus = {
    available: false,    // 扩展是否可用
    connected: false,    // 本地应用是否连接
    lastCheck: null      // 最后检查时间
};

// 检测扩展
function detectQMSManagerExtension() {
    if (window.QMSExtensionAvailable || document.getElementById('qms-extension-injected-indicator')) {
        qmsManagerStatus.available = true;
        checkQMSManagerConnection();
    } else {
        qmsManagerStatus.available = false;
        qmsManagerStatus.connected = false;
    }
    updateQMSManagerStatusDisplay();
}

// 检测本地应用连接
function checkQMSManagerConnection() {
    fetch('http://localhost:8765/ping', { method: 'GET', timeout: 3000 })
    .then(response => {
        qmsManagerStatus.connected = response.ok;
        updateQMSManagerStatusDisplay();
    })
    .catch(error => {
        qmsManagerStatus.connected = false;
        updateQMSManagerStatusDisplay();
    });
}
```

### 4. 用户界面增强

#### 状态指示器样式
```css
.qms-manager-status {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid #e0e0e0;
    font-size: 12px;
}

.status-indicator.connected #status-icon {
    color: #4caf50;  /* 绿色：已连接 */
}

.status-indicator.disconnected #status-icon {
    color: #ff9800;  /* 橙色：未连接 */
}
```

#### 下载路径设置增强
在下载路径设置对话框中添加了智能提示：

```html
<!-- QMS文件管理器已连接时显示 -->
<div id="qms-manager-recommendation" style="display: none;">
    <i class="fas fa-check-circle"></i>
    <strong>QMS文件管理器已连接：</strong>文件将自动下载到指定路径并打开。
</div>

<!-- QMS文件管理器未连接时显示 -->
<div id="browser-recommendation">
    <i class="fas fa-lightbulb"></i>
    <strong>建议：</strong>安装QMS文件管理器以获得更好的下载体验。
</div>
```

## 🎯 用户体验流程

### 1. 页面加载
```
页面加载 → 检测QMS文件管理器扩展 → 检测本地应用连接 → 更新状态显示
```

### 2. 文件下载
```
点击"打开" → 检查文件类型 → 检查QMS文件管理器状态 → 选择下载方式
```

### 3. 状态显示
- **已连接**：绿色圆点 + "QMS文件管理器 (已连接)"
- **未连接**：橙色圆点 + "QMS文件管理器 (未连接)"
- **不可用**：隐藏状态指示器

## 🔄 智能降级机制

系统实现了三级降级机制：

### 第一级：QMS文件管理器（最佳体验）
- **条件**：扩展已安装 + 本地应用已连接
- **效果**：文件直接下载到指定路径并自动打开
- **用户操作**：点击"打开" → 完成

### 第二级：浏览器下载（标准体验）
- **条件**：扩展未安装或本地应用未连接
- **效果**：显示下载指导对话框，使用浏览器下载
- **用户操作**：点击"打开" → 查看下载管理器 → 手动打开

### 第三级：传统下载（兼容体验）
- **条件**：所有智能功能不可用
- **效果**：直接使用浏览器默认下载
- **用户操作**：点击"打开" → 在下载文件夹中查找

## 📱 兼容性保证

### 向后兼容
- ✅ 保持原有功能完全不变
- ✅ 未安装QMS文件管理器的用户不受影响
- ✅ 所有现有的下载链接继续工作

### 渐进增强
- ✅ 安装QMS文件管理器后自动启用增强功能
- ✅ 本地应用离线时自动降级
- ✅ 扩展卸载后恢复传统模式

## 🔧 部署说明

### 前端代码部署
1. **备份现有文件**
   ```bash
   cp blueprints/incoming_inspection/templates/new_sampling_inspection.html \
      blueprints/incoming_inspection/templates/new_sampling_inspection.html.backup
   ```

2. **应用修改**
   - 使用修改后的`new_sampling_inspection.html`文件
   - 重启Flask应用

3. **验证功能**
   - 访问来料检验页面
   - 确认页面正常加载
   - 测试文件下载功能

### 客户端部署
1. **分发安装包**
   - 将`QMSFileManager`文件夹分发给用户
   - 提供安装指导文档

2. **用户安装**
   - 运行`install.bat`安装本地应用
   - 在Chrome中安装浏览器扩展

3. **功能验证**
   - 确认状态指示器显示"已连接"
   - 测试文件下载和自动打开

## 📊 功能测试

### 测试场景

#### 1. QMS文件管理器可用
```
预期：状态显示"已连接"，文件下载到指定路径并自动打开
测试：点击Excel附件"打开"按钮
结果：文件保存到C:\QMS1\并用Excel打开
```

#### 2. QMS文件管理器不可用
```
预期：使用传统下载方式，显示下载指导
测试：卸载扩展后点击"打开"按钮
结果：显示下载指导对话框，文件下载到浏览器默认文件夹
```

#### 3. 混合文件类型
```
预期：PDF预览，Office文档下载
测试：分别点击PDF和Excel附件
结果：PDF在浏览器中预览，Excel下载到本地
```

## 🎯 用户培训要点

### 对用户的说明
1. **安装QMS文件管理器的好处**
   - 文件自动下载到工作文件夹
   - 下载完成立即可用
   - 提高工作效率

2. **如何判断是否正常工作**
   - 页面右上角显示"QMS文件管理器 (已连接)"
   - 点击附件后文件自动下载并打开

3. **遇到问题怎么办**
   - 检查本地应用是否运行
   - 联系IT部门获取技术支持

### 对管理员的说明
1. **批量部署策略**
   - 先在部分用户中试点
   - 收集反馈后批量推广

2. **问题诊断**
   - 检查扩展安装状态
   - 确认本地应用运行状态
   - 查看浏览器控制台日志

---

**前端集成完成** - QMS系统现在支持智能文件下载，为用户提供更好的体验！
