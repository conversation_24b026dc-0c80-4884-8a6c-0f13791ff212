# 附件名称显示问题修复指南

## 🔍 问题描述

在物料管理页面上传附件后，附件列表中只显示文件扩展名（如"xlsx"），而不是完整的文件名。

## 🛠️ 问题原因

数据库中的 `material_attachments` 表的 `file_name` 字段可能存在以下问题：
1. 字段值为空字符串
2. 字段值为 NULL
3. 字段值只包含文件扩展名
4. 字段值过短（少于3个字符）

## ✅ 修复方法

### 方法1: 使用SQL直接修复（推荐）

1. **连接到MySQL数据库**
   ```bash
   mysql -u root -p quality_control
   ```

2. **查看有问题的附件**
   ```sql
   SELECT 
       id,
       material_id,
       file_name,
       file_extension,
       upload_time
   FROM material_attachments 
   WHERE is_active = TRUE 
   AND (
       file_name = '' 
       OR file_name IS NULL 
       OR file_name = file_extension 
       OR LENGTH(file_name) < 3
   )
   ORDER BY upload_time DESC;
   ```

3. **执行修复SQL**
   ```sql
   UPDATE material_attachments 
   SET file_name = CASE 
       WHEN file_name = '' OR file_name IS NULL THEN 
           CONCAT('附件_', id, CASE WHEN file_extension != '' THEN CONCAT('.', file_extension) ELSE '' END)
       WHEN file_name = file_extension THEN 
           CONCAT('附件_', id, CASE WHEN file_extension != '' THEN CONCAT('.', file_extension) ELSE '' END)
       WHEN LENGTH(file_name) < 3 THEN 
           CONCAT('附件_', id, CASE WHEN file_extension != '' THEN CONCAT('.', file_extension) ELSE '' END)
       ELSE file_name
   END
   WHERE is_active = TRUE 
   AND (
       file_name = '' 
       OR file_name IS NULL 
       OR file_name = file_extension 
       OR LENGTH(file_name) < 3
   );
   ```

4. **验证修复结果**
   ```sql
   SELECT 
       id,
       material_id,
       file_name,
       file_extension,
       upload_time
   FROM material_attachments 
   WHERE is_active = TRUE 
   ORDER BY upload_time DESC
   LIMIT 10;
   ```

### 方法2: 使用Python脚本修复

如果Python环境正常，可以运行：
```bash
python comprehensive_attachment_fix.py
```

### 方法3: 手动逐个修复

对于少量附件，可以手动修复：
```sql
-- 将附件ID为1的文件名修复为"附件_1.xlsx"
UPDATE material_attachments 
SET file_name = '附件_1.xlsx' 
WHERE id = 1;

-- 将附件ID为2的文件名修复为"附件_2.pdf"
UPDATE material_attachments 
SET file_name = '附件_2.pdf' 
WHERE id = 2;
```

## 🔧 预防措施

为了防止将来出现同样的问题，已经在API代码中实现了以下修复：

### 1. 改进的文件名处理函数

在 `blueprints/material_management/api.py` 中添加了 `safe_filename()` 函数：

```python
def safe_filename(filename):
    """安全的文件名处理，支持中文字符"""
    if not filename:
        return "unnamed_file"
    
    # 移除路径分隔符和危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 如果secure_filename返回空字符串（通常是因为全是非ASCII字符）
    secure_name = secure_filename(filename)
    if not secure_name or secure_name.strip() == '':
        # 保留原始文件名，只替换危险字符
        safe_name = re.sub(r'[^\w\s\-_\.\u4e00-\u9fff]', '_', filename)
        # 移除多余的下划线和空格
        safe_name = re.sub(r'[_\s]+', '_', safe_name).strip('_')
        return safe_name if safe_name else "unnamed_file"
    
    return secure_name
```

### 2. 上传API改进

文件上传时使用改进的文件名处理：
```python
# 生成安全的文件名
original_filename = safe_filename(file.filename)  # 使用新函数
unique_filename = f"{uuid.uuid4().hex}_{original_filename}"
```

## 📋 验证步骤

修复完成后，请按以下步骤验证：

1. **刷新浏览器页面**
   - 清除浏览器缓存
   - 重新加载物料编辑页面

2. **检查现有附件**
   - 查看附件列表是否显示完整文件名
   - 测试附件下载功能

3. **测试新上传**
   - 上传一个中文名称的文件
   - 验证文件名正确显示

4. **测试不同文件类型**
   - 测试图片文件（jpg, png）
   - 测试文档文件（pdf, docx, xlsx）
   - 测试图纸文件（dwg, dxf）

## 🚨 注意事项

1. **备份数据**: 在执行修复SQL之前，建议备份数据库
   ```bash
   mysqldump -u root -p quality_control material_attachments > attachments_backup.sql
   ```

2. **测试环境**: 建议先在测试环境中验证修复效果

3. **权限检查**: 确保数据库用户有UPDATE权限

4. **应用重启**: 修复完成后重启Flask应用以确保更改生效

## 🎯 预期结果

修复完成后，附件列表应该显示：
- ✅ 完整的文件名（如"产品规格书.xlsx"）
- ✅ 正确的文件图标
- ✅ 文件大小和上传时间
- ✅ 可以正常下载和打开

## 📞 故障排除

如果修复后仍有问题：

1. **检查浏览器控制台**: 查看是否有JavaScript错误
2. **检查网络请求**: 确认API返回的数据格式正确
3. **检查数据库**: 直接查询确认数据已更新
4. **重启服务**: 重启Flask应用和数据库服务

## 📝 相关文件

- `blueprints/material_management/api.py` - 附件上传API
- `blueprints/material_management/templates/material_management/edit_material.html` - 前端模板
- `database/create_material_attachments_table.py` - 数据库表创建脚本
- `comprehensive_attachment_fix.py` - 综合修复脚本
