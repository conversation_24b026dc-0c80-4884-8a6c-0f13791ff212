#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复附件数据问题
"""

import os
import sys
import re
from db_config import get_db_connection

def safe_filename(filename):
    """安全的文件名处理，支持中文字符"""
    if not filename:
        return "unnamed_file"
    
    # 移除路径分隔符和危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 如果是空字符串或只有空白字符
    if not filename.strip():
        return "unnamed_file"
    
    # 保留原始文件名，只替换危险字符
    safe_name = re.sub(r'[^\w\s\-_\.\u4e00-\u9fff]', '_', filename)
    # 移除多余的下划线和空格
    safe_name = re.sub(r'[_\s]+', '_', safe_name).strip('_')
    return safe_name if safe_name else "unnamed_file"

def fix_empty_filenames():
    """修复空文件名"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 查找文件名为空的记录...")
        
        # 查找文件名为空或NULL的记录
        cursor.execute("""
            SELECT id, material_id, file_name, file_path, file_extension
            FROM material_attachments 
            WHERE file_name = '' OR file_name IS NULL
            ORDER BY id
        """)
        
        empty_records = cursor.fetchall()
        
        if not empty_records:
            print("✅ 没有发现文件名为空的记录")
            return True
        
        print(f"❌ 发现 {len(empty_records)} 条文件名为空的记录")
        
        fixed_count = 0
        for record in empty_records:
            record_id = record['id']
            file_path = record['file_path']
            file_extension = record['file_extension']
            
            print(f"\n修复记录 ID: {record_id}")
            print(f"  文件路径: {file_path}")
            print(f"  文件扩展名: {file_extension}")
            
            # 尝试从文件路径中提取文件名
            new_filename = None
            
            if file_path:
                # 从路径中提取文件名
                path_filename = os.path.basename(file_path)
                if path_filename and path_filename != '.':
                    new_filename = path_filename
                    # 如果文件名包含UUID前缀，尝试提取原始名称
                    if '_' in path_filename:
                        parts = path_filename.split('_', 1)
                        if len(parts) > 1 and len(parts[0]) == 32:  # UUID长度
                            new_filename = parts[1]
            
            # 如果还是没有合适的文件名，生成一个
            if not new_filename:
                if file_extension:
                    new_filename = f"附件_{record_id}.{file_extension}"
                else:
                    new_filename = f"附件_{record_id}"
            
            # 确保文件名安全
            new_filename = safe_filename(new_filename)
            
            print(f"  新文件名: {new_filename}")
            
            # 更新数据库
            cursor.execute("""
                UPDATE material_attachments 
                SET file_name = %s 
                WHERE id = %s
            """, (new_filename, record_id))
            
            fixed_count += 1
        
        conn.commit()
        print(f"\n✅ 成功修复 {fixed_count} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def check_missing_files():
    """检查缺失的文件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("\n🔍 检查文件是否存在...")
        
        cursor.execute("""
            SELECT id, material_id, file_name, file_path
            FROM material_attachments 
            WHERE is_active = TRUE
            ORDER BY upload_time DESC
            LIMIT 20
        """)
        
        records = cursor.fetchall()
        
        if not records:
            print("✅ 没有附件记录")
            return True
        
        missing_count = 0
        base_dir = os.path.dirname(os.path.abspath(__file__))
        
        for record in records:
            file_path = record['file_path']
            
            # 构建完整路径
            if os.path.isabs(file_path):
                full_path = file_path
            else:
                full_path = os.path.join(base_dir, file_path)
            
            if not os.path.exists(full_path):
                missing_count += 1
                print(f"❌ 文件不存在: ID {record['id']}, 路径: {file_path}")
            else:
                print(f"✅ 文件存在: {record['file_name']}")
        
        if missing_count > 0:
            print(f"\n⚠️  发现 {missing_count} 个文件缺失")
        else:
            print(f"\n✅ 所有文件都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def create_upload_directories():
    """创建上传目录"""
    print("\n🔧 创建上传目录...")
    
    base_dir = os.path.dirname(os.path.abspath(__file__))
    upload_base = os.path.join(base_dir, 'static', 'uploads', 'material_attachments')
    
    subdirs = ['documents', 'images', 'drawings', 'certificates']
    
    for subdir in [''] + subdirs:
        dir_path = os.path.join(upload_base, subdir) if subdir else upload_base
        
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
            except Exception as e:
                print(f"❌ 创建目录失败: {dir_path}, 错误: {e}")
        else:
            print(f"✅ 目录已存在: {dir_path}")

def main():
    """主函数"""
    print("🚀 修复附件数据问题")
    print("=" * 50)
    
    # 1. 创建必要的目录
    create_upload_directories()
    
    # 2. 修复空文件名
    if not fix_empty_filenames():
        print("❌ 文件名修复失败")
        return
    
    # 3. 检查文件是否存在
    check_missing_files()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成")
    print("\n📋 修复内容:")
    print("1. ✅ 修复了空文件名问题")
    print("2. ✅ 检查了文件存在性")
    print("3. ✅ 创建了必要的目录")
    print("\n💡 建议:")
    print("- 重新启动应用以确保修改生效")
    print("- 测试附件上传和下载功能")

if __name__ == "__main__":
    main()
