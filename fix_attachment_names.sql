-- 修复附件文件名显示问题的SQL脚本
-- 用于修复 material_attachments 表中文件名显示不正确的问题

-- 1. 首先查看有问题的附件
SELECT 
    id,
    material_id,
    file_name,
    file_path,
    file_extension,
    upload_time,
    CASE 
        WHEN file_name = '' OR file_name IS NULL THEN '文件名为空'
        WHEN file_name = file_extension THEN '文件名只有扩展名'
        WHEN LENGTH(file_name) < 4 THEN '文件名过短'
        ELSE '正常'
    END as issue_type
FROM material_attachments 
WHERE is_active = TRUE
AND (
    file_name = '' 
    OR file_name IS NULL 
    OR file_name = file_extension 
    OR LENGTH(file_name) < 4
)
ORDER BY upload_time DESC;

-- 2. 修复文件名为空或只有扩展名的记录
-- 方法：从 file_path 中提取真实文件名，或生成有意义的文件名

-- 2.1 对于能从路径提取文件名的记录
UPDATE material_attachments 
SET file_name = SUBSTRING_INDEX(SUBSTRING_INDEX(file_path, '/', -1), '_', -1),
    updated_at = CURRENT_TIMESTAMP
WHERE is_active = TRUE
AND (file_name = '' OR file_name IS NULL OR file_name = file_extension)
AND file_path LIKE '%_%'
AND SUBSTRING_INDEX(SUBSTRING_INDEX(file_path, '/', -1), '_', -1) != ''
AND LENGTH(SUBSTRING_INDEX(SUBSTRING_INDEX(file_path, '/', -1), '_', -1)) > 3;

-- 2.2 对于无法从路径提取的记录，生成有意义的文件名
UPDATE material_attachments 
SET file_name = CONCAT('附件_', DATE_FORMAT(upload_time, '%Y%m%d_%H%i%s'), 
                      CASE WHEN file_extension != '' THEN CONCAT('.', file_extension) ELSE '' END),
    updated_at = CURRENT_TIMESTAMP
WHERE is_active = TRUE
AND (file_name = '' OR file_name IS NULL OR file_name = file_extension OR LENGTH(file_name) < 4);

-- 3. 验证修复结果
SELECT 
    id,
    material_id,
    file_name,
    file_extension,
    upload_time,
    CASE 
        WHEN file_name = '' OR file_name IS NULL THEN '仍有问题：文件名为空'
        WHEN file_name = file_extension THEN '仍有问题：文件名只有扩展名'
        WHEN LENGTH(file_name) < 4 THEN '仍有问题：文件名过短'
        ELSE '已修复'
    END as status
FROM material_attachments 
WHERE is_active = TRUE
ORDER BY updated_at DESC
LIMIT 20;

-- 4. 统计修复结果
SELECT 
    COUNT(*) as total_attachments,
    SUM(CASE WHEN file_name = '' OR file_name IS NULL OR file_name = file_extension OR LENGTH(file_name) < 4 THEN 1 ELSE 0 END) as problematic_count,
    SUM(CASE WHEN file_name != '' AND file_name IS NOT NULL AND file_name != file_extension AND LENGTH(file_name) >= 4 THEN 1 ELSE 0 END) as fixed_count
FROM material_attachments 
WHERE is_active = TRUE;
