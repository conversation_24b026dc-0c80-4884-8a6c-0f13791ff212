{% extends "base.html" %}

{% block title %}批量导入待检 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}{% endblock %}

{% block extra_css %}
<style>
    /* 采用与待检清单相同的简洁风格 */

    /* 页面标题和操作按钮区域 - 与抽样检验记录一致 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .header-left h1 {
        font-size: 18px;
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .header-right {
        display: flex;
        gap: 8px;
    }

    /* 按钮样式 - 与抽样检验记录一致 */
    .btn {
        padding: 2px 5px;
        font-size: 11px;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        border-radius: 4px;
        border: 1px solid transparent;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }

    .btn-primary {
        color: #fff;
        background-color: #1976d2;
        border-color: #1976d2;
    }

    .btn-primary:hover {
        background-color: #1565c0;
        border-color: #1565c0;
    }

    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    /* 简化的导入区域样式 */
    .import-section {
        margin-bottom: 10px;
    }

    .import-methods {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 10px;
    }

    .import-method {
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        background: #f8f9fa;
        transition: all 0.15s;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .import-method:hover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .import-method.active {
        border-color: #1976d2;
        background: #1976d2;
        color: white;
    }

    .import-method i {
        font-size: 12px;
    }

    .import-content {
        display: none;
        margin-top: 10px;
    }

    .import-content.active {
        display: block;
    }

    /* 表格样式 - 与抽样检验记录一致 */
    .manual-input-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .manual-input-table th, .manual-input-table td {
        padding: 3px 6px;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
    }

    .manual-input-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
    }

    .manual-input-table input,
    .manual-input-table select {
        width: 100%;
        padding: 2px 4px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 11px;
        height: 24px;
        box-sizing: border-box;
    }

    .manual-input-table input:focus,
    .manual-input-table select:focus {
        outline: none;
        border-color: #1976d2;
    }

    .add-row-btn {
        padding: 2px 5px;
        font-size: 11px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 5px;
    }

    .add-row-btn:hover {
        background: #218838;
    }

    .remove-row-btn {
        padding: 1px 3px;
        font-size: 10px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }

    .remove-row-btn:hover {
        background: #c82333;
    }

    /* 简化的文件上传区域 */
    .file-upload-area {
        border: 1px dashed #ddd;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
        font-size: 12px;
    }

    .file-upload-area:hover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .file-upload-area.dragover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .file-upload-area i {
        font-size: 24px;
        color: #666;
        margin-bottom: 8px;
    }

    .file-upload-area h3 {
        margin: 8px 0 5px 0;
        color: #333;
        font-size: 14px;
    }

    .file-upload-area p {
        color: #666;
        margin: 0;
        font-size: 11px;
    }

    .file-input {
        display: none;
    }

    .upload-btn {
        padding: 2px 5px;
        font-size: 11px;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 8px;
    }

    .upload-btn:hover {
        background: #1565c0;
    }

    .template-download {
        margin-top: 8px;
    }

    .template-download a {
        color: #1976d2;
        text-decoration: none;
        font-size: 11px;
    }

    .template-download a:hover {
        text-decoration: underline;
    }

    /* 简化的预览区域 */
    .preview-section {
        margin-bottom: 10px;
        display: none;
    }

    .preview-section.show {
        display: block;
    }

    /* 预览表格样式 - 与抽样检验记录一致 */
    .preview-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .preview-table th, .preview-table td {
        padding: 3px 6px;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
    }

    .preview-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
    }

    .preview-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .status-badge {
        padding: 1px 4px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: 500;
    }

    .status-success {
        background: #d4edda;
        color: #155724;
    }

    .status-warning {
        background: #fff3cd;
        color: #856404;
    }

    .status-error {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 10px;
    }

    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        background: #4caf50;
    }

    .toast.error {
        background: #f44336;
    }

    .toast.warning {
        background: #ff9800;
    }

    .loading {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .loading.show {
        display: block;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #2196f3;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="header-left">
        <h1>批量导入待检 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}</h1>
    </div>
    <div class="header-right">
        <a href="{{ url_for('incoming_inspection.pending_list', type='sampling') }}" class="btn btn-secondary">
            <i class="fas fa-list"></i> 待检清单
        </a>
        {% if inspection_type == 'sampling' %}
        <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 新增检验
        </a>
        {% else %}
        <a href="{{ url_for('full_inspection.new_inspection') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 新增检验
        </a>
        {% endif %}
    </div>
</div>

<!-- 简化的导入方式选择 -->
<div class="import-section">
    <div class="import-methods">
        <div class="import-method active" data-method="manual">
            <i class="fas fa-keyboard"></i> 手动录入
        </div>
        <div class="import-method" data-method="file">
            <i class="fas fa-file-excel"></i> 文件导入
        </div>
    </div>

        <!-- 手动录入内容 -->
        <div class="import-content active" id="manual-content">
            <table class="manual-input-table" id="manual-table">
                <thead>
                    <tr>
                        <th width="120">物料料号 <span style="color: red;">*</span></th>
                        <th width="150">物料名称</th>
                        <th width="150">规格</th>
                        <th width="120">供应商</th>
                        <th width="100">来料数量</th>
                        <th width="60">单位</th>
                        <th width="100">批次号</th>
                        <th width="100">到货日期</th>
                        <th width="80">操作</th>
                    </tr>
                </thead>
                <tbody id="manual-tbody">
                    <tr>
                        <td><input type="text" class="material-code" placeholder="输入料号"></td>
                        <td><input type="text" class="material-name" placeholder="自动获取"></td>
                        <td><input type="text" class="specification" placeholder="自动获取"></td>
                        <td><input type="text" class="supplier-name" placeholder="自动获取"></td>
                        <td><input type="number" class="incoming-quantity" placeholder="数量" step="0.001"></td>
                        <td><input type="text" class="unit" placeholder="单位"></td>
                        <td><input type="text" class="batch-number" placeholder="批次号"></td>
                        <td><input type="date" class="arrival-date"></td>
                        <td><button type="button" class="remove-row-btn">删除</button></td>
                    </tr>
                </tbody>
            </table>
            <button type="button" class="add-row-btn">
                <i class="fas fa-plus"></i> 添加行
            </button>
        </div>

        <!-- 文件导入内容 -->
        <div class="import-content" id="file-content">
            <div class="file-upload-area" id="upload-area">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>拖拽文件到此处或点击上传</h3>
                <p>支持 .xlsx, .xls 格式文件</p>
                <button type="button" class="upload-btn">
                    选择文件
                </button>
                <input type="file" id="file-input" class="file-input" accept=".xlsx,.xls">
            </div>
            <div class="template-download">
                <a href="#" class="download-template-btn">
                    <i class="fas fa-download"></i> 下载导入模板
                </a>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-secondary" id="clear-data-btn">
                <i class="fas fa-trash"></i> 清空数据
            </button>
            <button type="button" class="btn btn-primary" id="preview-data-btn">
                <i class="fas fa-eye"></i> 预览数据
            </button>
        </div>
    </div>

<!-- 数据预览区域 -->
<div class="preview-section" id="preview-section">
    <div id="preview-content">
        <!-- 预览内容将在这里动态生成 -->
    </div>
    <div class="action-buttons">
        <button type="button" class="btn btn-secondary" id="hide-preview-btn">
            <i class="fas fa-times"></i> 取消
        </button>
        <button type="button" class="btn btn-success" id="submit-data-btn">
            <i class="fas fa-save"></i> 确认导入
        </button>
    </div>
</div>

<!-- Toast 提示 -->
<div id="toast" class="toast"></div>
{% endblock %}

{% block extra_js %}
<script>
    // 清理可能的全局变量冲突
    if (typeof window.currentMethod !== 'undefined') {
        console.log('⚠️ 检测到currentMethod已存在，清理中...');
        delete window.currentMethod;
    }
    if (typeof window.previewData !== 'undefined') {
        console.log('⚠️ 检测到previewData已存在，清理中...');
        delete window.previewData;
    }

    // 重新声明变量
    let currentMethod = 'manual';
    let previewData = [];

    console.log('🔧 JavaScript变量初始化完成:', {
        currentMethod: currentMethod,
        previewDataLength: previewData.length
    });

    // 测试函数
    function testFileImport() {
        console.log('测试文件导入功能');
        alert('文件导入功能测试');
        switchImportMethod('file');
    }

    // 测试事件绑定函数
    function testEventBinding() {
        console.log('🧪 开始测试事件绑定...');

        const materialInputs = document.querySelectorAll('.material-code');
        console.log('🔍 找到料号输入框数量:', materialInputs.length);

        if (materialInputs.length > 0) {
            const firstInput = materialInputs[0];
            console.log('🧪 测试第一个输入框...');

            // 设置测试值
            firstInput.value = 'TEST001';
            console.log('📝 设置测试值: TEST001');

            // 手动触发blur事件
            firstInput.dispatchEvent(new Event('blur'));
            console.log('🎯 手动触发blur事件');

            // 检查是否有事件监听器
            const listeners = getEventListeners ? getEventListeners(firstInput) : 'getEventListeners不可用';
            console.log('👂 事件监听器:', listeners);
        } else {
            console.error('❌ 没有找到料号输入框');
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 DOM加载完成，开始初始化...');

        try {
            initializeEventListeners();
            console.log('✅ 事件监听器初始化成功');
        } catch (error) {
            console.error('❌ 事件监听器初始化失败:', error);
        }

        // 添加测试按钮
        setTimeout(() => {
            console.log('🔧 添加测试按钮...');

            // 文件导入测试按钮
            const testFileBtn = document.createElement('button');
            testFileBtn.textContent = '测试文件导入';
            testFileBtn.onclick = testFileImport;
            testFileBtn.style.cssText = 'position:fixed;top:10px;right:10px;z-index:9999;background:red;color:white;padding:5px;font-size:12px;';
            document.body.appendChild(testFileBtn);

            // 事件绑定测试按钮
            const testEventBtn = document.createElement('button');
            testEventBtn.textContent = '测试事件绑定';
            testEventBtn.onclick = testEventBinding;
            testEventBtn.style.cssText = 'position:fixed;top:50px;right:10px;z-index:9999;background:blue;color:white;padding:5px;font-size:12px;';
            document.body.appendChild(testEventBtn);

            console.log('✅ 测试按钮添加完成');
        }, 1000);
    });

    function initializeEventListeners() {
        console.log('🔧 开始初始化所有事件监听器...');

        // 1. 导入方式切换
        console.log('🔧 绑定导入方式切换事件...');
        const methods = document.querySelectorAll('.import-method');
        console.log('找到导入方式元素数量:', methods.length);

        methods.forEach(method => {
            console.log('绑定点击事件到:', method.dataset.method);
            method.addEventListener('click', function() {
                console.log('点击了导入方式:', this.dataset.method);
                switchImportMethod(this.dataset.method);
            });
        });

        // 2. 绑定料号输入框的事件
        console.log('🔧 绑定料号输入框事件...');
        bindMaterialCodeEvents();

        // 3. 绑定按钮事件
        console.log('🔧 绑定按钮事件...');
        bindButtonEvents();

        // 4. 文件拖拽
        console.log('🔧 绑定文件拖拽事件...');
        const uploadArea = document.getElementById('upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            console.log('✅ 文件拖拽事件绑定完成');
        } else {
            console.error('❌ 找不到上传区域元素');
        }

        console.log('✅ 所有事件监听器初始化完成');
    }

    function bindMaterialCodeEvents() {
        console.log('🔧 开始绑定料号输入框事件...');

        // 先移除所有现有的事件监听器，避免重复绑定
        const materialCodeInputs = document.querySelectorAll('.material-code');
        console.log('🔍 找到', materialCodeInputs.length, '个料号输入框');

        materialCodeInputs.forEach((input, index) => {
            console.log(`🔧 绑定第${index + 1}个输入框事件`);

            // 移除现有的事件监听器（如果有的话）
            const newInput = input.cloneNode(true);
            input.parentNode.replaceChild(newInput, input);

            // 失去焦点时触发（主要触发方式）
            newInput.addEventListener('blur', function() {
                console.log('🎯 触发事件：blur (失去焦点)', this.value);
                fetchMaterialInfo(this);
            });

            // 按Enter键时触发
            newInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    console.log('🎯 触发事件：Enter键', this.value);
                    e.preventDefault(); // 防止表单提交
                    fetchMaterialInfo(this);
                }
            });

            // 输入完成后停顿一段时间自动触发（防抖处理）
            let debounceTimer;
            newInput.addEventListener('input', function() {
                const currentValue = this.value.trim();
                console.log('📝 输入事件:', currentValue);

                // 清除之前的定时器
                if (debounceTimer) {
                    clearTimeout(debounceTimer);
                }

                // 如果输入框不为空，设置延迟触发
                if (currentValue.length > 0) {
                    debounceTimer = setTimeout(() => {
                        console.log('🎯 触发事件：输入停顿2秒后自动触发', currentValue);
                        fetchMaterialInfo(this);
                    }, 2000); // 2秒后自动触发
                }
            });

            console.log(`✅ 第${index + 1}个输入框事件绑定完成`);
        });

        console.log('✅ 所有料号输入框事件绑定完成，共', materialCodeInputs.length, '个');
    }

    function bindButtonEvents() {
        console.log('🔧 开始绑定按钮事件...');

        // 添加行按钮
        const addRowBtn = document.querySelector('.add-row-btn');
        if (addRowBtn) {
            addRowBtn.addEventListener('click', addRow);
            console.log('✅ 添加行按钮事件绑定完成');
        }

        // 删除行按钮（委托事件）
        const tbody = document.getElementById('manual-tbody');
        if (tbody) {
            tbody.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-row-btn')) {
                    console.log('🗑️ 点击删除行按钮');
                    removeRow(e.target);
                }
            });
            console.log('✅ 删除行按钮事件委托绑定完成');
        }

        // 清空数据按钮
        const clearDataBtn = document.getElementById('clear-data-btn');
        if (clearDataBtn) {
            clearDataBtn.addEventListener('click', clearData);
            console.log('✅ 清空数据按钮事件绑定完成');
        }

        // 预览数据按钮
        const previewDataBtn = document.getElementById('preview-data-btn');
        if (previewDataBtn) {
            previewDataBtn.addEventListener('click', previewData);
            console.log('✅ 预览数据按钮事件绑定完成');
        }

        // 隐藏预览按钮
        const hidePreviewBtn = document.getElementById('hide-preview-btn');
        if (hidePreviewBtn) {
            hidePreviewBtn.addEventListener('click', hidePreview);
            console.log('✅ 隐藏预览按钮事件绑定完成');
        }

        // 提交数据按钮
        const submitDataBtn = document.getElementById('submit-data-btn');
        if (submitDataBtn) {
            submitDataBtn.addEventListener('click', submitData);
            console.log('✅ 提交数据按钮事件绑定完成');
        }

        // 上传按钮
        const uploadBtn = document.querySelector('.upload-btn');
        const fileInput = document.getElementById('file-input');
        if (uploadBtn && fileInput) {
            uploadBtn.addEventListener('click', function() {
                fileInput.click();
            });
            fileInput.addEventListener('change', function() {
                handleFileSelect(this);
            });
            console.log('✅ 文件上传按钮事件绑定完成');
        }

        // 下载模板按钮
        const downloadTemplateBtn = document.querySelector('.download-template-btn');
        if (downloadTemplateBtn) {
            downloadTemplateBtn.addEventListener('click', function(e) {
                e.preventDefault();
                downloadTemplate();
            });
            console.log('✅ 下载模板按钮事件绑定完成');
        }

        console.log('✅ 所有按钮事件绑定完成');
    }

    function switchImportMethod(method) {
        console.log('切换导入方式:', method);
        currentMethod = method;

        // 更新方式选择样式
        document.querySelectorAll('.import-method').forEach(m => m.classList.remove('active'));
        const selectedMethod = document.querySelector(`[data-method="${method}"]`);
        if (selectedMethod) {
            selectedMethod.classList.add('active');
            console.log('已激活方式:', method);
        } else {
            console.error('找不到方式元素:', method);
        }

        // 切换内容显示
        document.querySelectorAll('.import-content').forEach(c => c.classList.remove('active'));
        const targetContent = document.getElementById(`${method}-content`);
        if (targetContent) {
            targetContent.classList.add('active');
            console.log('已显示内容:', method + '-content');
        } else {
            console.error('找不到内容元素:', method + '-content');
        }
    }

    function addRow() {
        const tbody = document.getElementById('manual-tbody');
        const newRow = tbody.rows[0].cloneNode(true);

        // 清空新行的输入值
        newRow.querySelectorAll('input').forEach(input => {
            input.value = '';
        });

        // 新行添加后，重新绑定所有料号输入框的事件
        console.log('🔧 新行添加完成，重新绑定料号输入框事件...');
        bindMaterialCodeEvents();

        tbody.appendChild(newRow);
    }

    function removeRow(btn) {
        const tbody = document.getElementById('manual-tbody');
        if (tbody.rows.length > 1) {
            btn.closest('tr').remove();
        } else {
            showToast('至少保留一行数据', 'warning');
        }
    }

    async function fetchMaterialInfo(input) {
        console.log('🚀 fetchMaterialInfo函数被调用');
        console.log('📝 输入元素:', input);
        console.log('📝 输入值:', input.value);

        const materialCode = input.value.trim();
        console.log('🔍 开始获取物料信息:', materialCode);

        if (!materialCode) {
            console.log('❌ 料号为空，跳过获取');
            return;
        }

        // 防止重复调用
        if (input.dataset.fetching === 'true') {
            console.log('⚠️ 正在获取中，跳过重复调用');
            return;
        }

        console.log('🔍 查找表格行...');
        const row = input.closest('tr');
        if (!row) {
            console.error('❌ 找不到表格行');
            return;
        }
        console.log('✅ 找到表格行:', row);

        const materialNameInput = row.querySelector('.material-name');
        const specificationInput = row.querySelector('.specification');
        const supplierNameInput = row.querySelector('.supplier-name');

        console.log('🔍 查找输入框元素:');
        console.log('  - 物料名称输入框:', materialNameInput);
        console.log('  - 规格输入框:', specificationInput);
        console.log('  - 供应商输入框:', supplierNameInput);

        if (!materialNameInput || !specificationInput || !supplierNameInput) {
            console.error('❌ 找不到必要的输入框元素');
            return;
        }

        // 检查是否已经获取过相同的料号
        const currentName = materialNameInput.value;
        const currentSpec = specificationInput.value;

        if (currentName && currentSpec && input.dataset.lastFetched === materialCode) {
            console.log('✅ 该料号信息已获取，跳过重复获取');
            return;
        }

        // 标记正在获取
        input.dataset.fetching = 'true';
        console.log('🏁 设置获取标记为true');

        try {
            // 获取物料基本信息
            const materialUrl = `/api/material_info/${materialCode}`;
            console.log('📡 请求物料信息URL:', materialUrl);

            console.log('🌐 开始发送fetch请求...');
            const response = await fetch(materialUrl);
            console.log('📡 物料信息响应状态:', response.status);
            console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                console.error('❌ HTTP响应错误:', response.status, response.statusText);
                showToast(`API请求失败: ${response.status} ${response.statusText}`, 'error');
                return;
            }

            console.log('🔄 解析JSON响应...');
            const data = await response.json();
            console.log('📋 物料信息响应数据:', JSON.stringify(data, null, 2));

            if (data.success && data.material) {
                const material = data.material;
                console.log('✅ 物料信息获取成功:', material);

                console.log('📝 开始填充物料信息...');
                materialNameInput.value = material.material_name || '';
                specificationInput.value = material.specification || '';
                console.log('✅ 物料基本信息已填充');
                console.log('  - 物料名称:', materialNameInput.value);
                console.log('  - 规格:', specificationInput.value);

                // 简化供应商信息获取，先跳过以排除问题
                console.log('⚠️ 暂时跳过供应商信息获取，专注测试物料信息');

                showToast(`物料 ${materialCode} 信息获取成功`, 'success');

                // 标记已成功获取
                input.dataset.lastFetched = materialCode;
                console.log('✅ 标记料号已获取:', materialCode);
            } else {
                console.log('❌ 未找到物料信息或响应失败');
                console.log('📋 响应数据详情:', data);

                // 如果没找到物料信息，清空相关字段
                materialNameInput.value = '';
                specificationInput.value = '';
                supplierNameInput.value = '';

                const errorMsg = data.error || '未找到物料信息';
                showToast(`物料 ${materialCode}: ${errorMsg}`, 'warning');
            }
        } catch (error) {
            console.error('❌ 获取物料信息失败:', error);
            console.error('❌ 错误详情:', error.message);
            console.error('❌ 错误堆栈:', error.stack);
            showToast('获取物料信息失败，请检查网络连接', 'error');
        } finally {
            // 清除获取中标记
            input.dataset.fetching = 'false';
            console.log('🏁 物料信息获取完成，清除获取标记');
        }
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.currentTarget.classList.remove('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect({ files: files });
        }
    }

    function handleFileSelect(input) {
        console.log('文件选择事件触发');
        const file = input.files[0];
        if (!file) {
            console.log('没有选择文件');
            return;
        }

        console.log('选择的文件:', file.name, '大小:', file.size, '类型:', file.type);

        if (!file.name.match(/\.(xlsx|xls)$/)) {
            console.log('文件格式不正确');
            showToast('请选择Excel文件', 'error');
            return;
        }

        console.log('文件格式正确，开始处理');
        // 这里可以添加文件解析逻辑
        showToast('文件上传成功，请点击预览数据', 'success');
    }

    function downloadTemplate() {
        // 创建模板数据
        const templateData = [
            ['物料料号', '物料名称', '规格', '供应商', '来料数量', '单位', '批次号', '到货日期'],
            ['示例001', '示例物料', '10*20*30', '示例供应商', '100', 'PCS', 'BATCH001', '2024-01-01']
        ];
        
        // 这里可以使用库如 SheetJS 来生成Excel文件
        showToast('模板下载功能开发中', 'warning');
    }

    function clearData() {
        if (currentMethod === 'manual') {
            const tbody = document.getElementById('manual-tbody');
            tbody.innerHTML = `
                <tr>
                    <td><input type="text" class="material-code" placeholder="输入料号"></td>
                    <td><input type="text" class="material-name" placeholder="自动获取"></td>
                    <td><input type="text" class="specification" placeholder="自动获取"></td>
                    <td><input type="text" class="supplier-name" placeholder="自动获取"></td>
                    <td><input type="number" class="incoming-quantity" placeholder="数量" step="0.001"></td>
                    <td><input type="text" class="unit" placeholder="单位"></td>
                    <td><input type="text" class="batch-number" placeholder="批次号"></td>
                    <td><input type="date" class="arrival-date"></td>
                    <td><button type="button" class="remove-row-btn">删除</button></td>
                </tr>
            `;
            // 重新绑定事件
            bindMaterialCodeEvents();
        } else {
            document.getElementById('file-input').value = '';
        }
        
        hidePreview();
        showToast('数据已清空', 'success');
    }

    function previewData() {
        if (currentMethod === 'manual') {
            previewManualData();
        } else {
            previewFileData();
        }
    }

    function previewManualData() {
        const rows = document.querySelectorAll('#manual-tbody tr');
        previewData.length = 0; // 清空数组而不是重新声明

        rows.forEach(row => {
            const materialCode = row.querySelector('.material-code').value.trim();
            if (materialCode) {
                previewData.push({
                    material_code: materialCode,
                    material_name: row.querySelector('.material-name').value.trim(),
                    specification: row.querySelector('.specification').value.trim(),
                    supplier_name: row.querySelector('.supplier-name').value.trim(),
                    incoming_quantity: row.querySelector('.incoming-quantity').value,
                    unit: row.querySelector('.unit').value.trim(),
                    batch_number: row.querySelector('.batch-number').value.trim(),
                    arrival_date: row.querySelector('.arrival-date').value
                });
            }
        });
        
        if (previewData.length === 0) {
            showToast('请至少输入一个物料料号', 'warning');
            return;
        }
        
        showPreview();
    }

    function previewFileData() {
        // 文件数据预览逻辑
        showToast('文件预览功能开发中', 'warning');
    }

    function showPreview() {
        const previewContent = document.getElementById('preview-content');
        
        let html = `
            <p>共 ${previewData.length} 条记录</p>
            <table class="preview-table">
                <thead>
                    <tr>
                        <th>物料料号</th>
                        <th>物料名称</th>
                        <th>规格</th>
                        <th>供应商</th>
                        <th>来料数量</th>
                        <th>单位</th>
                        <th>批次号</th>
                        <th>到货日期</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        previewData.forEach(item => {
            const status = item.material_code ? 'success' : 'error';
            const statusText = item.material_code ? '正常' : '料号为空';
            
            html += `
                <tr>
                    <td>${item.material_code || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.specification || ''}</td>
                    <td>${item.supplier_name || ''}</td>
                    <td>${item.incoming_quantity || ''}</td>
                    <td>${item.unit || ''}</td>
                    <td>${item.batch_number || ''}</td>
                    <td>${item.arrival_date || ''}</td>
                    <td><span class="status-badge status-${status}">${statusText}</span></td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        previewContent.innerHTML = html;
        
        document.getElementById('preview-section').classList.add('show');
    }

    function hidePreview() {
        document.getElementById('preview-section').classList.remove('show');
    }

    async function submitData() {
        if (previewData.length === 0) {
            showToast('没有数据可提交', 'warning');
            return;
        }

        showLoading();
        
        try {
            const response = await fetch('/pending_inspection/api/pending_inspections/batch_import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inspection_type: '{{ inspection_type }}',
                    materials: previewData,
                    batch_name: `{{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showToast(`导入成功！成功导入 ${data.data.success_count} 个物料`, 'success');
                
                // 3秒后跳转到待检清单
                setTimeout(() => {
                    window.location.href = '/incoming_inspection/pending_list?type={{ inspection_type }}';
                }, 3000);
            } else {
                showToast(`导入失败：${data.error}`, 'error');
            }
        } catch (error) {
            showToast('导入失败，请重试', 'error');
            console.error('导入失败:', error);
        } finally {
            hideLoading();
        }
    }

    function showLoading() {
        document.getElementById('loading').classList.add('show');
    }

    function hideLoading() {
        document.getElementById('loading').classList.remove('show');
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type} show`;
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
</script>
{% endblock %}
