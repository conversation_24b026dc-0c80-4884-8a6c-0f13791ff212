{% extends "base.html" %}

{% block title %}批量导入待检 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}{% endblock %}

{% block extra_css %}
<style>
    /* 与抽样检验记录完全一致的样式 */

    /* 页面标题和操作按钮区域 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .header-left h1 {
        font-size: 18px;
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .header-right {
        display: flex;
        gap: 8px;
    }

    /* 按钮样式 - 与抽样检验记录一致 */
    .btn {
        padding: 2px 5px;
        font-size: 11px;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        border-radius: 4px;
        border: 1px solid transparent;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }

    .btn-primary {
        color: #fff;
        background-color: #1976d2;
        border-color: #1976d2;
    }

    .btn-primary:hover {
        background-color: #1565c0;
        border-color: #1565c0;
    }

    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    /* 分页样式 - 与抽样检验记录一致 */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .pagination a, .pagination span {
        padding: 3px 6px;
        text-decoration: none;
        border: 1px solid #ddd;
        margin: 0 2px;
        color: #333;
        font-size: 11px;
    }

    .pagination a:hover {
        background-color: #f1f1f1;
    }

    .pagination .active {
        background-color: #1976d2;
        color: white;
        border: 1px solid #1976d2;
    }

    .pagination .disabled {
        color: #aaa;
        border: 1px solid #ddd;
    }

    .summary {
        margin-bottom: 5px;
        color: #666;
        font-size: 0.75em;
    }

    /* 分页容器样式 */
    .pagination-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding: 5px 0;
    }

    .page-size-selector {
        font-size: 11px;
        color: #666;
        display: flex;
        align-items: center;
        white-space: nowrap;
        min-width: 130px;
    }

    .page-size-selector select {
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 11px;
        margin-left: 5px;
        cursor: pointer;
        width: 65px;
    }

    .pagination-controls {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }

    /* 简化的导入区域样式 */
    .import-section {
        margin-bottom: 10px;
    }

    .import-methods {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 10px;
    }

    .import-method {
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        background: #f8f9fa;
        transition: all 0.15s;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .import-method:hover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .import-method.active {
        border-color: #1976d2;
        background: #1976d2;
        color: white;
    }

    .import-method i {
        font-size: 12px;
    }

    .import-content {
        display: none;
        margin-top: 10px;
    }

    .import-content.active {
        display: block;
    }

    /* 表格样式 - 与抽样检验记录完全一致 */
    .manual-input-table, .sortable-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .manual-input-table th, .manual-input-table td,
    .sortable-table th, .sortable-table td {
        padding: 3px 6px;
        white-space: nowrap;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
    }

    .manual-input-table th, .sortable-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    /* 表格容器 */
    .table-container {
        margin: 0;
        padding: 0;
        width: 100%;
        box-sizing: border-box;
        height: calc(100vh - 200px);
        min-height: 400px;
        overflow-x: auto;
    }

    /* 紧凑表格行 */
    .manual-input-table tr, .sortable-table tr {
        height: 24px;
        transition: background-color 0.2s;
    }

    .manual-input-table tbody tr:nth-child(even),
    .sortable-table tbody tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .manual-input-table tbody tr:hover,
    .sortable-table tbody tr:hover {
        background-color: #eaf2fd;
    }

    .manual-input-table input,
    .manual-input-table select {
        width: 100%;
        padding: 2px 4px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 11px;
        height: 24px;
        box-sizing: border-box;
        background-color: transparent;  /* 去除背景色 */
    }

    .manual-input-table select {
        background-color: #fff;  /* select元素需要白色背景 */
        cursor: pointer;
    }

    .manual-input-table input:focus,
    .manual-input-table select:focus {
        outline: none;
        border-color: #1976d2;
        background-color: #fff;  /* 聚焦时显示白色背景 */
    }

    /* 自动获取字段的特殊样式（可选） */
    .manual-input-table input.auto-filled,
    .manual-input-table select.auto-filled {
        background-color: #f8fff8;  /* 淡绿色背景表示已自动填充 */
    }

    .manual-input-table input.auto-filled:focus,
    .manual-input-table select.auto-filled:focus {
        background-color: #fff;  /* 聚焦时恢复白色背景 */
    }

    /* 免检物料的特殊样式 */
    .manual-input-table select.inspection-type option[value="免检"] {
        background-color: #fff3cd;  /* 免检选项黄色背景 */
        color: #856404;
    }

    .manual-input-table select.inspection-type[value="免检"] {
        background-color: #fff3cd;  /* 免检选择时的背景色 */
        color: #856404;
        border-color: #ffc107;
    }

    /* 免检行的整体样式调整 */
    .manual-input-table tr.exempt-row {
        background-color: #fffbf0;  /* 免检行的浅黄色背景 */
    }

    .manual-input-table tr.exempt-row:hover {
        background-color: #fff3cd;  /* 免检行悬停时的背景色 */
    }

    .add-row-btn {
        padding: 2px 5px;
        font-size: 11px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 5px;
    }

    .add-row-btn:hover {
        background: #218838;
    }

    .remove-row-btn {
        padding: 1px 3px;
        font-size: 10px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }

    .remove-row-btn:hover {
        background: #c82333;
    }

    /* 简化的文件上传区域 */
    .file-upload-area {
        border: 1px dashed #ddd;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
        font-size: 12px;
    }

    .file-upload-area:hover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .file-upload-area.dragover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .file-upload-area i {
        font-size: 24px;
        color: #666;
        margin-bottom: 8px;
    }

    .file-upload-area h3 {
        margin: 8px 0 5px 0;
        color: #333;
        font-size: 14px;
    }

    .file-upload-area p {
        color: #666;
        margin: 0;
        font-size: 11px;
    }

    .file-input {
        display: none;
    }

    .upload-btn {
        padding: 2px 5px;
        font-size: 11px;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 8px;
    }

    .upload-btn:hover {
        background: #1565c0;
    }

    .template-download {
        margin-top: 8px;
    }

    .template-download a {
        color: #1976d2;
        text-decoration: none;
        font-size: 11px;
    }

    .template-download a:hover {
        text-decoration: underline;
    }

    /* 简化的预览区域 */
    .preview-section {
        margin-bottom: 10px;
        display: none;
    }

    .preview-section.show {
        display: block;
    }

    /* 预览表格样式 - 与抽样检验记录完全一致 */
    .preview-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .preview-table th, .preview-table td {
        padding: 3px 6px;
        white-space: nowrap;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
    }

    .preview-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .preview-table tr {
        height: 24px;
        transition: background-color 0.2s;
    }

    .preview-table tbody tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .preview-table tbody tr:hover {
        background-color: #eaf2fd;
    }

    .status-badge {
        padding: 1px 4px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: 500;
    }

    .status-success {
        background: #d4edda;
        color: #155724;
    }

    .status-warning {
        background: #fff3cd;
        color: #856404;
    }

    .status-error {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 10px;
    }

    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        background: #4caf50;
    }

    .toast.error {
        background: #f44336;
    }

    .toast.warning {
        background: #ff9800;
    }

    .toast.info {
        background: #17a2b8;
    }

    .loading {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .loading.show {
        display: block;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #2196f3;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 移动端响应式分页样式 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-wrap: nowrap;
            gap: 5px;
        }

        .summary {
            order: 1;
            min-width: 0;
            text-align: left;
            margin-right: 5px;
        }

        .pagination {
            order: 2;
            width: auto;
            margin-top: 0;
            flex-grow: 1;
            justify-content: center;
        }

        .page-size-selector {
            order: 3;
            width: auto;
            min-width: 0;
            justify-content: flex-end;
        }

        .pagination a, .pagination span {
            padding: 3px 6px;
        }
    }

    @media (max-width: 480px) {
        .pagination a, .pagination span {
            padding: 4px 6px;
            min-width: 20px;
            font-size: 12px;
        }

        .page-size-selector {
            font-size: 10px;
        }

        .page-size-selector select {
            width: 50px;
            font-size: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="header-left">
        <h1>批量导入待检 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}</h1>
    </div>
    <div class="header-right">
        <a href="{{ url_for('incoming_inspection.pending_list', type='sampling') }}" class="btn btn-secondary">
            <i class="fas fa-list"></i> 待检清单
        </a>
        {% if inspection_type == 'sampling' %}
        <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 新增检验
        </a>
        {% else %}
        <a href="{{ url_for('full_inspection.new_inspection') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 新增检验
        </a>
        {% endif %}
    </div>
</div>

<!-- 简化的导入方式选择 -->
<div class="import-section">
    <div class="import-methods">
        <div class="import-method active" data-method="manual">
            <i class="fas fa-keyboard"></i> 手动录入
        </div>
        <div class="import-method" data-method="file">
            <i class="fas fa-file-excel"></i> 文件导入
        </div>
    </div>

        <!-- 手动录入内容 -->
        <div class="import-content active" id="manual-content">
            <div class="table-container">
                <table class="manual-input-table sortable-table" id="manual-table">
                    <thead>
                        <tr>
                            <th width="100">物料料号 <span style="color: red;">*</span></th>
                            <th width="120">物料名称</th>
                            <th width="120">规格</th>
                            <th width="80">材质</th>
                            <th width="60">颜色</th>
                            <th width="80">物料类型</th>
                            <th width="80">检验类型</th>
                            <th width="100">供应商</th>
                            <th width="80">来料数量</th>
                            <th width="50">单位</th>
                            <th width="80">批次号</th>
                            <th width="100">到货日期</th>
                            <th width="60">操作</th>
                        </tr>
                    </thead>
                    <tbody id="manual-tbody">
                        <tr>
                            <td><input type="text" class="material-code" placeholder="输入料号" title="输入料号后自动获取物料信息"></td>
                            <td><input type="text" class="material-name" placeholder="物料名称" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td><input type="text" class="specification" placeholder="规格" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td><input type="text" class="material-type" placeholder="材质" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td><input type="text" class="color" placeholder="颜色" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td><input type="text" class="material-category" placeholder="物料类型" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td>
                                <select class="inspection-type" title="选择检验类型：抽样检验、全部检验或免检">
                                    <option value="">请选择检验类型</option>
                                    <option value="抽样">抽样检验</option>
                                    <option value="全部">全部检验</option>
                                    <option value="免检">免检</option>
                                </select>
                            </td>
                            <td><input type="text" class="supplier-name" placeholder="供应商" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td><input type="number" class="incoming-quantity" placeholder="数量" step="0.001" title="手动输入来料数量"></td>
                            <td><input type="text" class="unit" placeholder="单位" title="可自动获取或手动输入，双击清除自动填充"></td>
                            <td><input type="text" class="batch-number" placeholder="批次号" title="手动输入批次号"></td>
                            <td><input type="date" class="arrival-date" title="选择到货日期"></td>
                            <td><button type="button" class="remove-row-btn" title="删除此行">删除</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页和记录统计信息 -->
            <div class="pagination-container" style="display: flex; align-items: center; justify-content: space-between; margin-top: 2px; padding: 2px 0; flex-wrap: nowrap;">
                <div class="summary" style="color: #666; white-space: nowrap; font-size: 11px; min-width: auto;">
                    显示 <span id="current-rows">1</span>/<span id="total-rows">1</span> 行
                </div>

                <div class="pagination" style="display: flex; align-items: center; margin: 0; flex-grow: 1; justify-content: center;">
                    <span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">首页</span>
                    <span class="disabled page-nav-btn prev-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">上一页</span>
                    <span class="current-page-display" style="display: inline-block; margin: 0 1px; padding: 1px 8px; background-color: #1976d2; border: 1px solid #1976d2; color: white; min-width: 15px; text-align: center;">1</span>
                    <span class="disabled page-nav-btn next-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">下一页</span>
                    <span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">末页</span>
                </div>

                <div class="page-size-selector" style="font-size: 11px; color: #666; display: flex; align-items: center; white-space: nowrap; min-width: auto; justify-content: flex-end;">
                    每页:
                    <select id="per-page-selector" style="padding: 0 2px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px; margin-left: 3px; height: 20px; width: 45px;">
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>

            <button type="button" class="add-row-btn">
                <i class="fas fa-plus"></i> 添加行
            </button>
        </div>

        <!-- 文件导入内容 -->
        <div class="import-content" id="file-content">
            <div class="file-upload-area" id="upload-area">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>拖拽文件到此处或点击上传</h3>
                <p>支持 .xlsx, .xls 格式文件</p>
                <button type="button" class="upload-btn">
                    选择文件
                </button>
                <input type="file" id="file-input" class="file-input" accept=".xlsx,.xls">
            </div>
            <div class="template-download">
                <a href="#" class="download-template-btn">
                    <i class="fas fa-download"></i> 下载导入模板
                </a>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-secondary" id="clear-data-btn">
                <i class="fas fa-trash"></i> 清空数据
            </button>
            <button type="button" class="btn btn-primary" id="preview-data-btn">
                <i class="fas fa-eye"></i> 预览数据
            </button>
        </div>
    </div>

<!-- 数据预览区域 -->
<div class="preview-section" id="preview-section">
    <div class="table-container">
        <div id="preview-content">
            <!-- 预览内容将在这里动态生成 -->
        </div>
    </div>

    <!-- 预览区域分页 -->
    <div class="pagination-container" id="preview-pagination" style="display: none; align-items: center; justify-content: space-between; margin-top: 2px; padding: 2px 0; flex-wrap: nowrap;">
        <div class="summary" style="color: #666; white-space: nowrap; font-size: 11px; min-width: auto;">
            预览 <span id="preview-current-rows">0</span>/<span id="preview-total-rows">0</span> 条
        </div>

        <div class="pagination" id="preview-pagination-controls" style="display: flex; align-items: center; margin: 0; flex-grow: 1; justify-content: center;">
            <!-- 分页控件将由JavaScript动态生成 -->
        </div>

        <div class="page-size-selector" style="font-size: 11px; color: #666; display: flex; align-items: center; white-space: nowrap; min-width: auto; justify-content: flex-end;">
            每页:
            <select id="preview-per-page-selector" style="padding: 0 2px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px; margin-left: 3px; height: 20px; width: 45px;">
                <option value="10" selected>10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
        </div>
    </div>

    <div class="action-buttons">
        <button type="button" class="btn btn-secondary" id="hide-preview-btn">
            <i class="fas fa-times"></i> 取消
        </button>
        <button type="button" class="btn btn-success" id="submit-data-btn">
            <i class="fas fa-save"></i> 确认导入
        </button>
    </div>
</div>

<!-- Toast 提示 -->
<div id="toast" class="toast"></div>
{% endblock %}

{% block extra_js %}
<script>
(function() {
    'use strict';

    console.log('🔧 开始初始化批量导入页面JavaScript...');

    // 使用立即执行函数表达式(IIFE)创建独立作用域，避免全局变量冲突
    var currentMethod = 'manual';
    var previewData = [];

    // 分页相关变量
    var currentPage = 1;
    var perPage = 10;
    var totalRows = 1;

    // 预览分页变量
    var previewCurrentPage = 1;
    var previewPerPage = 10;
    var previewTotalRows = 0;

    console.log('🔧 JavaScript变量初始化完成:', {
        currentMethod: currentMethod,
        previewDataLength: previewData.length,
        currentPage: currentPage,
        perPage: perPage
    });

    // 测试函数
    function testFileImport() {
        console.log('测试文件导入功能');
        alert('文件导入功能测试');
        switchImportMethod('file');
    }

    // 测试事件绑定函数
    function testEventBinding() {
        console.log('🧪 开始测试事件绑定...');

        const materialInputs = document.querySelectorAll('.material-code');
        console.log('🔍 找到料号输入框数量:', materialInputs.length);

        if (materialInputs.length > 0) {
            const firstInput = materialInputs[0];
            console.log('🧪 测试第一个输入框...');

            // 设置测试值
            firstInput.value = 'TEST001';
            console.log('📝 设置测试值: TEST001');

            // 手动触发blur事件
            firstInput.dispatchEvent(new Event('blur'));
            console.log('🎯 手动触发blur事件');

            // 检查事件绑定（简化版本）
            console.log('👂 事件监听器已绑定（getEventListeners在生产环境不可用）');
        } else {
            console.error('❌ 没有找到料号输入框');
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 DOM加载完成，开始初始化...');

        try {
            initializeEventListeners();
            bindAutoFillFieldEvents(); // 绑定自动填充字段事件
            bindInspectionTypeEvents(); // 绑定检验类型变化事件
            updateRowCount(); // 初始化行数统计
            console.log('✅ 事件监听器初始化成功');
        } catch (error) {
            console.error('❌ 事件监听器初始化失败:', error);
        }

        // 添加测试按钮
        setTimeout(() => {
            console.log('🔧 添加测试按钮...');

            // 文件导入测试按钮
            const testFileBtn = document.createElement('button');
            testFileBtn.textContent = '测试文件导入';
            testFileBtn.onclick = testFileImport;
            testFileBtn.style.cssText = 'position:fixed;top:10px;right:10px;z-index:9999;background:red;color:white;padding:5px;font-size:12px;';
            document.body.appendChild(testFileBtn);

            // 事件绑定测试按钮
            const testEventBtn = document.createElement('button');
            testEventBtn.textContent = '测试事件绑定';
            testEventBtn.onclick = testEventBinding;
            testEventBtn.style.cssText = 'position:fixed;top:50px;right:10px;z-index:9999;background:blue;color:white;padding:5px;font-size:12px;';
            document.body.appendChild(testEventBtn);

            console.log('✅ 测试按钮添加完成');
        }, 1000);
    });

    function initializeEventListeners() {
        console.log('🔧 开始初始化所有事件监听器...');

        // 1. 导入方式切换
        console.log('🔧 绑定导入方式切换事件...');
        const methods = document.querySelectorAll('.import-method');
        console.log('找到导入方式元素数量:', methods.length);

        methods.forEach(method => {
            console.log('绑定点击事件到:', method.dataset.method);
            method.addEventListener('click', function() {
                console.log('点击了导入方式:', this.dataset.method);
                switchImportMethod(this.dataset.method);
            });
        });

        // 2. 绑定料号输入框的事件
        console.log('🔧 绑定料号输入框事件...');
        bindMaterialCodeEvents();

        // 3. 绑定按钮事件
        console.log('🔧 绑定按钮事件...');
        bindButtonEvents();

        // 4. 文件拖拽
        console.log('🔧 绑定文件拖拽事件...');
        const uploadArea = document.getElementById('upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            console.log('✅ 文件拖拽事件绑定完成');
        } else {
            console.error('❌ 找不到上传区域元素');
        }

        console.log('✅ 所有事件监听器初始化完成');
    }

    function bindMaterialCodeEvents() {
        console.log('🔧 开始绑定料号输入框事件...');

        // 先移除所有现有的事件监听器，避免重复绑定
        const materialCodeInputs = document.querySelectorAll('.material-code');
        console.log('🔍 找到', materialCodeInputs.length, '个料号输入框');

        materialCodeInputs.forEach((input, index) => {
            console.log(`🔧 绑定第${index + 1}个输入框事件`);

            // 移除现有的事件监听器（如果有的话）
            const newInput = input.cloneNode(true);
            input.parentNode.replaceChild(newInput, input);

            // 失去焦点时触发（主要触发方式）
            newInput.addEventListener('blur', function() {
                console.log('🎯 触发事件：blur (失去焦点)', this.value);
                fetchMaterialInfo(this);
            });

            // 按Enter键时触发
            newInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    console.log('🎯 触发事件：Enter键', this.value);
                    e.preventDefault(); // 防止表单提交
                    fetchMaterialInfo(this);
                }
            });

            // 输入完成后停顿一段时间自动触发（防抖处理）
            let debounceTimer;
            newInput.addEventListener('input', function() {
                const currentValue = this.value.trim();
                console.log('📝 输入事件:', currentValue);

                // 清除之前的定时器
                if (debounceTimer) {
                    clearTimeout(debounceTimer);
                }

                // 如果输入框不为空，设置延迟触发
                if (currentValue.length > 0) {
                    debounceTimer = setTimeout(() => {
                        console.log('🎯 触发事件：输入停顿2秒后自动触发', currentValue);
                        fetchMaterialInfo(this);
                    }, 2000); // 2秒后自动触发
                }
            });

            console.log(`✅ 第${index + 1}个输入框事件绑定完成`);
        });

        console.log('✅ 所有料号输入框事件绑定完成，共', materialCodeInputs.length, '个');
    }

    function bindButtonEvents() {
        console.log('🔧 开始绑定按钮事件...');

        // 添加行按钮
        const addRowBtn = document.querySelector('.add-row-btn');
        if (addRowBtn) {
            addRowBtn.addEventListener('click', addRow);
            console.log('✅ 添加行按钮事件绑定完成');
        }

        // 删除行按钮（委托事件）
        const tbody = document.getElementById('manual-tbody');
        if (tbody) {
            tbody.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-row-btn')) {
                    console.log('🗑️ 点击删除行按钮');
                    removeRow(e.target);
                }
            });
            console.log('✅ 删除行按钮事件委托绑定完成');
        }

        // 清空数据按钮
        const clearDataBtn = document.getElementById('clear-data-btn');
        if (clearDataBtn) {
            clearDataBtn.addEventListener('click', clearData);
            console.log('✅ 清空数据按钮事件绑定完成');
        }

        // 预览数据按钮
        const previewDataBtn = document.getElementById('preview-data-btn');
        if (previewDataBtn) {
            previewDataBtn.addEventListener('click', previewData);
            console.log('✅ 预览数据按钮事件绑定完成');
        }

        // 隐藏预览按钮
        const hidePreviewBtn = document.getElementById('hide-preview-btn');
        if (hidePreviewBtn) {
            hidePreviewBtn.addEventListener('click', hidePreview);
            console.log('✅ 隐藏预览按钮事件绑定完成');
        }

        // 提交数据按钮
        const submitDataBtn = document.getElementById('submit-data-btn');
        if (submitDataBtn) {
            submitDataBtn.addEventListener('click', submitData);
            console.log('✅ 提交数据按钮事件绑定完成');
        }

        // 上传按钮
        const uploadBtn = document.querySelector('.upload-btn');
        const fileInput = document.getElementById('file-input');
        if (uploadBtn && fileInput) {
            uploadBtn.addEventListener('click', function() {
                fileInput.click();
            });
            fileInput.addEventListener('change', function() {
                handleFileSelect(this);
            });
            console.log('✅ 文件上传按钮事件绑定完成');
        }

        // 下载模板按钮
        const downloadTemplateBtn = document.querySelector('.download-template-btn');
        if (downloadTemplateBtn) {
            downloadTemplateBtn.addEventListener('click', function(e) {
                e.preventDefault();
                downloadTemplate();
            });
            console.log('✅ 下载模板按钮事件绑定完成');
        }

        // 分页选择器
        const perPageSelector = document.getElementById('per-page-selector');
        if (perPageSelector) {
            perPageSelector.addEventListener('change', function() {
                perPage = parseInt(this.value);
                console.log('📄 每页显示行数已更改:', perPage);
                // 这里可以添加实际的分页逻辑
            });
            console.log('✅ 分页选择器事件绑定完成');
        }

        // 预览分页选择器
        const previewPerPageSelector = document.getElementById('preview-per-page-selector');
        if (previewPerPageSelector) {
            previewPerPageSelector.addEventListener('change', function() {
                previewPerPage = parseInt(this.value);
                previewCurrentPage = 1; // 重置到第一页
                console.log('📄 预览每页显示行数已更改:', previewPerPage);
                updatePreviewDisplay();
                updatePreviewPagination();
            });
            console.log('✅ 预览分页选择器事件绑定完成');
        }

        console.log('✅ 所有按钮事件绑定完成');
    }

    function switchImportMethod(method) {
        console.log('切换导入方式:', method);
        currentMethod = method;

        // 更新方式选择样式
        document.querySelectorAll('.import-method').forEach(m => m.classList.remove('active'));
        const selectedMethod = document.querySelector(`[data-method="${method}"]`);
        if (selectedMethod) {
            selectedMethod.classList.add('active');
            console.log('已激活方式:', method);
        } else {
            console.error('找不到方式元素:', method);
        }

        // 切换内容显示
        document.querySelectorAll('.import-content').forEach(c => c.classList.remove('active'));
        const targetContent = document.getElementById(`${method}-content`);
        if (targetContent) {
            targetContent.classList.add('active');
            console.log('已显示内容:', method + '-content');
        } else {
            console.error('找不到内容元素:', method + '-content');
        }
    }

    function addRow() {
        const tbody = document.getElementById('manual-tbody');
        const newRow = tbody.rows[0].cloneNode(true);

        // 清空新行的输入值和样式
        newRow.querySelectorAll('input, select').forEach(element => {
            if (element.tagName === 'SELECT') {
                element.selectedIndex = 0; // 重置select到第一个选项
            } else {
                element.value = '';
            }
            // 移除自动填充的样式标识
            element.classList.remove('auto-filled');
        });

        tbody.appendChild(newRow);

        // 更新行数统计
        updateRowCount();

        // 新行添加后，重新绑定所有事件
        console.log('🔧 新行添加完成，重新绑定事件...');
        bindMaterialCodeEvents();
        bindAutoFillFieldEvents();
        bindInspectionTypeEvents();
    }

    function bindAutoFillFieldEvents() {
        // 为自动填充字段绑定双击清除事件
        const autoFillSelectors = ['.material-name', '.specification', '.material-type', '.color', '.material-category', '.inspection-type', '.supplier-name', '.unit'];

        autoFillSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                // 移除之前的事件监听器（如果有）
                element.removeEventListener('dblclick', clearAutoFillField);
                // 添加新的事件监听器
                element.addEventListener('dblclick', clearAutoFillField);
            });
        });

        console.log('✅ 自动填充字段双击清除事件绑定完成');
    }

    function bindInspectionTypeEvents() {
        // 为检验类型下拉框绑定变化事件
        document.querySelectorAll('.inspection-type').forEach(select => {
            // 移除之前的事件监听器（如果有）
            select.removeEventListener('change', handleInspectionTypeChange);
            // 添加新的事件监听器
            select.addEventListener('change', handleInspectionTypeChange);
        });

        console.log('✅ 检验类型变化事件绑定完成');
    }

    function handleInspectionTypeChange(event) {
        const select = event.target;
        const selectedValue = select.value;
        const row = select.closest('tr');

        console.log('🔄 检验类型已更改:', selectedValue);

        // 根据检验类型变化的逻辑
        if (selectedValue === '抽样') {
            console.log('📊 选择了抽样检验');
            // 抽样检验相关的逻辑
            // 可以在这里添加抽样比例设置、抽样数量计算等
        } else if (selectedValue === '全部') {
            console.log('📋 选择了全部检验');
            // 全部检验相关的逻辑
            // 可以在这里添加全检项目设置、检验标准等
        } else if (selectedValue === '免检') {
            console.log('🚫 选择了免检');
            // 免检相关的逻辑
            // 可以在这里添加免检原因记录、免检审批等

            // 给用户一个提示
            const materialCode = row.querySelector('.material-code').value;
            if (materialCode) {
                showToast(`物料 ${materialCode} 已设置为免检`, 'info');
            }
        } else if (selectedValue === '') {
            console.log('⚪ 未选择检验类型');
        }

        // 根据检验类型调整行样式
        if (selectedValue === '免检') {
            row.classList.add('exempt-row');
        } else {
            row.classList.remove('exempt-row');
        }

        // 可以在这里添加更多根据检验类型的界面调整
        // 例如：显示/隐藏特定字段，调整验证规则等
    }

    function clearAutoFillField(event) {
        const element = event.target;
        if (element.classList.contains('auto-filled')) {
            if (element.tagName === 'SELECT') {
                element.selectedIndex = 0; // 重置select到第一个选项（通常是空选项）
            } else {
                element.value = '';
            }
            element.classList.remove('auto-filled');
            console.log('🗑️ 已清除自动填充内容:', element.className);
        }
    }

    function updateRowCount() {
        const tbody = document.getElementById('manual-tbody');
        const totalRows = tbody.rows.length;

        document.getElementById('current-rows').textContent = totalRows;
        document.getElementById('total-rows').textContent = totalRows;

        console.log('📊 更新行数统计:', totalRows);
    }

    function removeRow(btn) {
        const tbody = document.getElementById('manual-tbody');
        if (tbody.rows.length > 1) {
            btn.closest('tr').remove();
            updateRowCount();
        } else {
            showToast('至少保留一行数据', 'warning');
        }
    }

    async function fetchMaterialInfo(input) {
        console.log('🚀 fetchMaterialInfo函数被调用');
        console.log('📝 输入元素:', input);
        console.log('📝 输入值:', input.value);

        const materialCode = input.value.trim();
        console.log('🔍 开始获取物料信息:', materialCode);

        if (!materialCode) {
            console.log('❌ 料号为空，跳过获取');
            return;
        }

        // 防止重复调用
        if (input.dataset.fetching === 'true') {
            console.log('⚠️ 正在获取中，跳过重复调用');
            return;
        }

        console.log('🔍 查找表格行...');
        const row = input.closest('tr');
        if (!row) {
            console.error('❌ 找不到表格行');
            return;
        }
        console.log('✅ 找到表格行:', row);

        const materialNameInput = row.querySelector('.material-name');
        const specificationInput = row.querySelector('.specification');
        const materialTypeInput = row.querySelector('.material-type');
        const colorInput = row.querySelector('.color');
        const materialCategoryInput = row.querySelector('.material-category');
        const inspectionTypeInput = row.querySelector('.inspection-type');
        const supplierNameInput = row.querySelector('.supplier-name');
        const unitInput = row.querySelector('.unit');

        console.log('🔍 查找输入框元素:');
        console.log('  - 物料名称输入框:', materialNameInput);
        console.log('  - 规格输入框:', specificationInput);
        console.log('  - 材质输入框:', materialTypeInput);
        console.log('  - 颜色输入框:', colorInput);
        console.log('  - 物料类型输入框:', materialCategoryInput);
        console.log('  - 检验类型输入框:', inspectionTypeInput);
        console.log('  - 供应商输入框:', supplierNameInput);
        console.log('  - 单位输入框:', unitInput);

        if (!materialNameInput || !specificationInput || !supplierNameInput) {
            console.error('❌ 找不到必要的输入框元素');
            return;
        }

        // 检查是否已经获取过相同的料号
        const currentName = materialNameInput.value;
        const currentSpec = specificationInput.value;

        if (currentName && currentSpec && input.dataset.lastFetched === materialCode) {
            console.log('✅ 该料号信息已获取，跳过重复获取');
            return;
        }

        // 标记正在获取
        input.dataset.fetching = 'true';
        console.log('🏁 设置获取标记为true');

        try {
            // 获取物料基本信息
            const materialUrl = `/api/material_info/${materialCode}`;
            console.log('📡 请求物料信息URL:', materialUrl);

            console.log('🌐 开始发送fetch请求...');
            const response = await fetch(materialUrl);
            console.log('📡 物料信息响应状态:', response.status);
            console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                console.error('❌ HTTP响应错误:', response.status, response.statusText);
                showToast(`API请求失败: ${response.status} ${response.statusText}`, 'error');
                return;
            }

            console.log('🔄 解析JSON响应...');
            const data = await response.json();
            console.log('📋 物料信息响应数据:', JSON.stringify(data, null, 2));

            if (data.success && data.material) {
                const material = data.material;
                console.log('✅ 物料信息获取成功:', material);

                console.log('📝 开始填充物料信息...');

                // 定义自动填充的字段
                const autoFillInputs = [materialNameInput, specificationInput, materialTypeInput, colorInput, materialCategoryInput, inspectionTypeInput, unitInput];

                // 填充基本信息
                materialNameInput.value = material.material_name || '';
                specificationInput.value = material.specification || '';

                // 填充材质信息
                if (materialTypeInput) {
                    materialTypeInput.value = material.material_type || '';
                    console.log('✅ 材质信息已填充:', material.material_type || '无');
                }

                // 填充颜色信息
                if (colorInput) {
                    colorInput.value = material.color || '';
                    console.log('✅ 颜色信息已填充:', material.color || '无');
                }

                // 填充物料类型信息
                if (materialCategoryInput) {
                    materialCategoryInput.value = material.material_category || '';
                    console.log('✅ 物料类型已填充:', material.material_category || '无');
                }

                // 填充检验类型信息
                if (inspectionTypeInput) {
                    const inspectionType = material.inspection_type || '';
                    console.log('🔍 物料检验类型:', inspectionType);

                    // 处理select元素的值设置
                    if (inspectionType) {
                        // 标准化检验类型值
                        let normalizedType = '';
                        if (inspectionType === '抽样' || inspectionType.includes('抽样')) {
                            normalizedType = '抽样';
                        } else if (inspectionType === '全检' || inspectionType === '全部' || inspectionType.includes('全部') || inspectionType.includes('全检')) {
                            normalizedType = '全部';
                        } else if (inspectionType === '免检' || inspectionType.includes('免检')) {
                            normalizedType = '免检';
                        } else {
                            // 如果是其他值，默认选择抽样检验
                            normalizedType = '抽样';
                        }

                        inspectionTypeInput.value = normalizedType;
                        console.log('✅ 检验类型已填充:', normalizedType, '(原值:', inspectionType + ')');

                        // 如果是免检，给出特殊提示并设置行样式
                        if (normalizedType === '免检') {
                            console.log('🚫 该物料为免检物料');
                            row.classList.add('exempt-row');
                        } else {
                            row.classList.remove('exempt-row');
                        }
                    } else {
                        // 如果没有检验类型信息，默认选择抽样检验
                        inspectionTypeInput.value = '抽样';
                        console.log('✅ 检验类型已填充: 抽样 (默认值)');
                    }
                }

                // 填充单位信息（暂时设置默认值）
                if (unitInput) {
                    unitInput.value = material.unit || '个';
                    console.log('✅ 单位信息已填充:', material.unit || '个（默认）');
                }

                // 添加自动填充的视觉标识
                autoFillInputs.forEach(input => {
                    if (input && input.value) {
                        input.classList.add('auto-filled');
                        // 3秒后移除标识
                        setTimeout(() => {
                            input.classList.remove('auto-filled');
                        }, 3000);
                    }
                });

                console.log('✅ 物料完整信息已填充');
                console.log('  - 物料名称:', materialNameInput.value);
                console.log('  - 规格:', specificationInput.value);
                console.log('  - 材质:', materialTypeInput ? materialTypeInput.value : 'N/A');
                console.log('  - 颜色:', colorInput ? colorInput.value : 'N/A');
                console.log('  - 物料类型:', materialCategoryInput ? materialCategoryInput.value : 'N/A');
                console.log('  - 检验类型:', inspectionTypeInput ? inspectionTypeInput.value : 'N/A');
                console.log('  - 单位:', unitInput ? unitInput.value : 'N/A');

                // 获取最近供应商信息
                console.log('🔍 开始获取最近供应商信息...');
                try {
                    const supplierUrl = `/incoming/api/recent_supplier/${materialCode}`;
                    console.log('📡 请求供应商信息URL:', supplierUrl);

                    const supplierResponse = await fetch(supplierUrl);
                    console.log('📡 供应商信息响应状态:', supplierResponse.status);

                    if (supplierResponse.ok) {
                        const supplierData = await supplierResponse.json();
                        console.log('📋 供应商信息响应数据:', JSON.stringify(supplierData, null, 2));

                        if (supplierData.success && supplierData.supplier) {
                            supplierNameInput.value = supplierData.supplier;
                            // 添加自动填充的视觉标识
                            supplierNameInput.classList.add('auto-filled');
                            setTimeout(() => {
                                supplierNameInput.classList.remove('auto-filled');
                            }, 3000);
                            console.log('✅ 供应商信息已填充:', supplierData.supplier);
                        } else {
                            console.log('⚠️ 未找到最近的供应商信息');
                            supplierNameInput.value = '';
                        }
                    } else {
                        console.log('❌ 供应商API请求失败:', supplierResponse.status);
                        supplierNameInput.value = '';
                    }
                } catch (supplierError) {
                    console.error('❌ 获取供应商信息失败:', supplierError);
                    supplierNameInput.value = '';
                }

                showToast(`物料 ${materialCode} 信息获取成功`, 'success');

                // 标记已成功获取
                input.dataset.lastFetched = materialCode;
                console.log('✅ 标记料号已获取:', materialCode);
            } else {
                console.log('❌ 未找到物料信息或响应失败');
                console.log('📋 响应数据详情:', data);

                // 如果没找到物料信息，清空相关字段
                materialNameInput.value = '';
                specificationInput.value = '';
                supplierNameInput.value = '';

                const errorMsg = data.error || '未找到物料信息';
                showToast(`物料 ${materialCode}: ${errorMsg}`, 'warning');
            }
        } catch (error) {
            console.error('❌ 获取物料信息失败:', error);
            console.error('❌ 错误详情:', error.message);
            console.error('❌ 错误堆栈:', error.stack);
            showToast('获取物料信息失败，请检查网络连接', 'error');
        } finally {
            // 清除获取中标记
            input.dataset.fetching = 'false';
            console.log('🏁 物料信息获取完成，清除获取标记');
        }
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.currentTarget.classList.remove('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect({ files: files });
        }
    }

    function handleFileSelect(input) {
        console.log('文件选择事件触发');
        const file = input.files[0];
        if (!file) {
            console.log('没有选择文件');
            return;
        }

        console.log('选择的文件:', file.name, '大小:', file.size, '类型:', file.type);

        if (!file.name.match(/\.(xlsx|xls)$/)) {
            console.log('文件格式不正确');
            showToast('请选择Excel文件', 'error');
            return;
        }

        console.log('文件格式正确，开始处理');
        // 这里可以添加文件解析逻辑
        showToast('文件上传成功，请点击预览数据', 'success');
    }

    function downloadTemplate() {
        // 创建模板数据
        const templateData = [
            ['物料料号', '物料名称', '规格', '供应商', '来料数量', '单位', '批次号', '到货日期'],
            ['示例001', '示例物料', '10*20*30', '示例供应商', '100', 'PCS', 'BATCH001', '2024-01-01']
        ];
        
        // 这里可以使用库如 SheetJS 来生成Excel文件
        showToast('模板下载功能开发中', 'warning');
    }

    function clearData() {
        if (currentMethod === 'manual') {
            const tbody = document.getElementById('manual-tbody');
            tbody.innerHTML = `
                <tr>
                    <td><input type="text" class="material-code" placeholder="输入料号" title="输入料号后自动获取物料信息"></td>
                    <td><input type="text" class="material-name" placeholder="物料名称" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td><input type="text" class="specification" placeholder="规格" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td><input type="text" class="material-type" placeholder="材质" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td><input type="text" class="color" placeholder="颜色" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td><input type="text" class="material-category" placeholder="物料类型" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td>
                        <select class="inspection-type" title="选择检验类型：抽样检验、全部检验或免检">
                            <option value="">请选择检验类型</option>
                            <option value="抽样">抽样检验</option>
                            <option value="全部">全部检验</option>
                            <option value="免检">免检</option>
                        </select>
                    </td>
                    <td><input type="text" class="supplier-name" placeholder="供应商" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td><input type="number" class="incoming-quantity" placeholder="数量" step="0.001" title="手动输入来料数量"></td>
                    <td><input type="text" class="unit" placeholder="单位" title="可自动获取或手动输入，双击清除自动填充"></td>
                    <td><input type="text" class="batch-number" placeholder="批次号" title="手动输入批次号"></td>
                    <td><input type="date" class="arrival-date" title="选择到货日期"></td>
                    <td><button type="button" class="remove-row-btn" title="删除此行">删除</button></td>
                </tr>
            `;
            // 重新绑定事件
            bindMaterialCodeEvents();
            // 更新行数统计
            updateRowCount();
        } else {
            document.getElementById('file-input').value = '';
        }

        hidePreview();
        showToast('数据已清空', 'success');
    }

    function previewData() {
        if (currentMethod === 'manual') {
            previewManualData();
        } else {
            previewFileData();
        }
    }

    function previewManualData() {
        const rows = document.querySelectorAll('#manual-tbody tr');
        previewData.length = 0; // 清空数组而不是重新声明

        rows.forEach(row => {
            const materialCode = row.querySelector('.material-code').value.trim();
            if (materialCode) {
                previewData.push({
                    material_code: materialCode,
                    material_name: row.querySelector('.material-name').value.trim(),
                    specification: row.querySelector('.specification').value.trim(),
                    material_type: row.querySelector('.material-type').value.trim(),
                    color: row.querySelector('.color').value.trim(),
                    material_category: row.querySelector('.material-category').value.trim(),
                    inspection_type: row.querySelector('.inspection-type').value,
                    supplier_name: row.querySelector('.supplier-name').value.trim(),
                    incoming_quantity: row.querySelector('.incoming-quantity').value,
                    unit: row.querySelector('.unit').value.trim(),
                    batch_number: row.querySelector('.batch-number').value.trim(),
                    arrival_date: row.querySelector('.arrival-date').value
                });
            }
        });

        if (previewData.length === 0) {
            showToast('请至少输入一个物料料号', 'warning');
            return;
        }

        showPreview();
    }

    function previewFileData() {
        // 文件数据预览逻辑
        showToast('文件预览功能开发中', 'warning');
    }

    function showPreview() {
        previewTotalRows = previewData.length;
        previewCurrentPage = 1;

        updatePreviewDisplay();
        updatePreviewPagination();

        document.getElementById('preview-section').classList.add('show');
        document.getElementById('preview-pagination').style.display = 'flex';
    }

    function updatePreviewDisplay() {
        const previewContent = document.getElementById('preview-content');
        const startIndex = (previewCurrentPage - 1) * previewPerPage;
        const endIndex = Math.min(startIndex + previewPerPage, previewTotalRows);
        const currentPageData = previewData.slice(startIndex, endIndex);

        let html = `
            <table class="preview-table sortable-table">
                <thead>
                    <tr>
                        <th>物料料号</th>
                        <th>物料名称</th>
                        <th>规格</th>
                        <th>材质</th>
                        <th>颜色</th>
                        <th>物料类型</th>
                        <th>检验类型</th>
                        <th>供应商</th>
                        <th>来料数量</th>
                        <th>单位</th>
                        <th>批次号</th>
                        <th>到货日期</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
        `;

        currentPageData.forEach(item => {
            const status = item.material_code ? 'success' : 'error';
            const statusText = item.material_code ? '正常' : '料号为空';

            html += `
                <tr>
                    <td>${item.material_code || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.specification || ''}</td>
                    <td>${item.material_type || ''}</td>
                    <td>${item.color || ''}</td>
                    <td>${item.material_category || ''}</td>
                    <td>${item.inspection_type || ''}</td>
                    <td>${item.supplier_name || ''}</td>
                    <td>${item.incoming_quantity || ''}</td>
                    <td>${item.unit || ''}</td>
                    <td>${item.batch_number || ''}</td>
                    <td>${item.arrival_date || ''}</td>
                    <td><span class="status-badge status-${status}">${statusText}</span></td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        previewContent.innerHTML = html;

        // 更新统计信息
        document.getElementById('preview-current-rows').textContent = currentPageData.length;
        document.getElementById('preview-total-rows').textContent = previewTotalRows;
    }

    function updatePreviewPagination() {
        const totalPages = Math.ceil(previewTotalRows / previewPerPage);
        const paginationControls = document.getElementById('preview-pagination-controls');

        let html = '';

        // 首页和上一页
        if (previewCurrentPage > 1) {
            html += `<a href="#" onclick="goToPreviewPage(1)" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f1f1f1; border: 1px solid #ddd;">首页</a>`;
            html += `<a href="#" onclick="goToPreviewPage(${previewCurrentPage - 1})" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f1f1f1; border: 1px solid #ddd;">上一页</a>`;
        } else {
            html += `<span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">首页</span>`;
            html += `<span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">上一页</span>`;
        }

        // 当前页
        html += `<span class="current-page-display" style="display: inline-block; margin: 0 1px; padding: 1px 8px; background-color: #1976d2; border: 1px solid #1976d2; color: white; min-width: 15px; text-align: center;">${previewCurrentPage}</span>`;

        // 下一页和末页
        if (previewCurrentPage < totalPages) {
            html += `<a href="#" onclick="goToPreviewPage(${previewCurrentPage + 1})" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f1f1f1; border: 1px solid #ddd;">下一页</a>`;
            html += `<a href="#" onclick="goToPreviewPage(${totalPages})" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f1f1f1; border: 1px solid #ddd;">末页</a>`;
        } else {
            html += `<span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">下一页</span>`;
            html += `<span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">末页</span>`;
        }

        paginationControls.innerHTML = html;
    }

    // 全局函数，供分页链接调用
    window.goToPreviewPage = function(page) {
        previewCurrentPage = page;
        updatePreviewDisplay();
        updatePreviewPagination();
    };

    function hidePreview() {
        document.getElementById('preview-section').classList.remove('show');
    }

    async function submitData() {
        if (previewData.length === 0) {
            showToast('没有数据可提交', 'warning');
            return;
        }

        showLoading();
        
        try {
            const response = await fetch('/pending_inspection/api/pending_inspections/batch_import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inspection_type: '{{ inspection_type }}',
                    materials: previewData,
                    batch_name: `{{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showToast(`导入成功！成功导入 ${data.data.success_count} 个物料`, 'success');
                
                // 3秒后跳转到待检清单
                setTimeout(() => {
                    window.location.href = '/incoming_inspection/pending_list?type={{ inspection_type }}';
                }, 3000);
            } else {
                showToast(`导入失败：${data.error}`, 'error');
            }
        } catch (error) {
            showToast('导入失败，请重试', 'error');
            console.error('导入失败:', error);
        } finally {
            hideLoading();
        }
    }

    function showLoading() {
        document.getElementById('loading').classList.add('show');
    }

    function hideLoading() {
        document.getElementById('loading').classList.remove('show');
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type} show`;

        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    console.log('✅ 批量导入页面JavaScript初始化完成');
})(); // 立即执行函数表达式结束
</script>
{% endblock %}
