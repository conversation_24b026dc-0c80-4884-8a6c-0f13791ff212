#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的附件文件名修复脚本
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from db_config import get_db_connection
    print("✅ 成功导入数据库配置")
except ImportError as e:
    print(f"❌ 导入数据库配置失败: {e}")
    sys.exit(1)

def fix_filenames():
    """修复文件名"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 查找需要修复的附件...")
        
        # 查找有问题的附件
        cursor.execute("""
            SELECT 
                id, 
                material_id, 
                file_name, 
                file_path, 
                file_extension, 
                upload_time
            FROM material_attachments 
            WHERE is_active = TRUE
            AND (
                file_name = '' 
                OR file_name IS NULL 
                OR file_name = file_extension 
                OR LENGTH(file_name) < 4
            )
            ORDER BY upload_time DESC
        """)
        
        problematic = cursor.fetchall()
        
        if not problematic:
            print("✅ 没有发现需要修复的附件")
            return
        
        print(f"📋 发现 {len(problematic)} 个需要修复的附件")
        
        for att in problematic:
            att_id = att['id']
            current_name = att['file_name'] or ''
            file_path = att['file_path'] or ''
            file_ext = att['file_extension'] or ''
            upload_time = att['upload_time']
            
            print(f"\n📄 ID: {att_id}, 当前名称: '{current_name}', 扩展名: '{file_ext}'")
            
            # 尝试从路径提取文件名
            new_name = None
            if file_path:
                basename = os.path.basename(file_path)
                if '_' in basename:
                    parts = basename.split('_', 1)
                    if len(parts) > 1 and len(parts[1]) > 3:
                        new_name = parts[1]
                        print(f"   从路径提取: '{new_name}'")
            
            # 如果无法提取，生成文件名
            if not new_name:
                timestamp = upload_time.strftime('%Y%m%d_%H%M%S') if upload_time else '20240101_000000'
                new_name = f"附件_{timestamp}.{file_ext}" if file_ext else f"附件_{timestamp}"
                print(f"   生成名称: '{new_name}'")
            
            # 更新数据库
            cursor.execute("""
                UPDATE material_attachments 
                SET file_name = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (new_name, att_id))
            
            print(f"   ✅ 已更新")
        
        conn.commit()
        print(f"\n🎉 修复完成！共修复 {len(problematic)} 个附件")
        
        # 验证结果
        cursor.execute("""
            SELECT id, file_name, file_extension
            FROM material_attachments 
            WHERE is_active = TRUE
            ORDER BY updated_at DESC
            LIMIT 5
        """)
        
        recent = cursor.fetchall()
        print("\n📋 最近更新的附件:")
        for r in recent:
            print(f"   ID {r['id']}: '{r['file_name']}'")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 开始修复附件文件名...")
    fix_filenames()
    print("🏁 完成")
