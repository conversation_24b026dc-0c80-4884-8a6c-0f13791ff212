{% extends "base.html" %}

{% block title %}待检清单 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}{% endblock %}

{% block extra_css %}
<style>
    /* 采用与抽样检验记录页面相同的简洁风格 */

    /* 页面标题和操作按钮区域 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e0e0e0;
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .header-left h1 {
        font-size: 18px;
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    /* 搜索框样式 */
    .quick-search-form {
        position: relative;
        margin-right: 8px;
    }

    .quick-search-input {
        height: 32px;
        padding: 2px 30px 2px 8px;
        font-size: 12px;
        width: 300px;
        max-width: 300px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #ddd;
    }

    .search-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        cursor: pointer;
        font-size: 12px;
    }

    /* 按钮样式 */
    .buttons-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        padding: 6px 12px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 4px;
        border: 1px solid transparent;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }

    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .btn-primary {
        color: #fff;
        background-color: #1976d2;
        border-color: #1976d2;
    }

    .btn-primary:hover {
        background-color: #1565c0;
        border-color: #1565c0;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-sm {
        padding: 2px 6px;
        font-size: 11px;
        line-height: 1.5;
        border-radius: 3px;
    }
    }

    /* 简洁的筛选区域 */
    .filter-section {
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #e0e0e0;
    }

    .filter-row {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .filter-group label {
        font-size: 12px;
        color: #666;
        white-space: nowrap;
    }

    .filter-group select {
        height: 32px;
        padding: 2px 8px;
        font-size: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 100px;
    }

    /* 简洁的表格样式 - 与抽样检验记录一致 */
    .table-container {
        margin-top: 10px;
    }

    .sortable-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 12px;
        background-color: white;
    }

    .sortable-table th {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 8px 6px;
        text-align: left;
        font-weight: 600;
        color: #495057;
        cursor: pointer;
        user-select: none;
        position: relative;
        font-size: 11px;
    }

    .sortable-table th:hover {
        background-color: #e9ecef;
    }

    .sortable-table td {
        border: 1px solid #dee2e6;
        padding: 6px;
        vertical-align: middle;
        font-size: 11px;
        line-height: 1.3;
    }

    .sortable-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* 状态标签样式 */
    .status-badge {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        min-width: 60px;
        display: inline-block;
    }

    .status-pending {
        background: #fff3e0;
        color: #f57c00;
    }

    .status-progress {
        background: #e3f2fd;
        color: #1976d2;
    }

    .status-completed {
        background: #e8f5e8;
        color: #2e7d32;
    }

    .status-cancelled {
        background: #ffebee;
        color: #c62828;
    }

    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 3px;
        flex-wrap: wrap;
    }

    .btn-warning {
        color: #fff;
        background-color: #ffc107;
        border-color: #ffc107;
    }

    .btn-warning:hover {
        background-color: #e0a800;
        border-color: #d39e00;
    }

    .btn-danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* 简洁的分页样式 - 与抽样检验记录一致 */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .pagination a, .pagination span {
        padding: 3px 6px;
        text-decoration: none;
        border: 1px solid #ddd;
        margin: 0 2px;
        color: #333;
        font-size: 11px;
    }

    .pagination a:hover {
        background-color: #f1f1f1;
    }

    .pagination .active {
        background-color: #1976d2;
        color: white;
        border: 1px solid #1976d2;
    }

    .pagination .disabled {
        color: #aaa;
        border: 1px solid #ddd;
    }

    /* 分页容器样式 */
    .pagination-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding: 5px 0;
    }

    .summary {
        margin-bottom: 5px;
        color: #666;
        font-size: 0.75em;
    }

    /* 简洁的空状态和加载状态 */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #666;
        font-size: 12px;
    }

    .loading {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 12px;
    }

    /* 简洁的提示消息 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 4px;
        color: white;
        font-size: 12px;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .toast.show {
        opacity: 1;
    }

    .toast.success {
        background: #28a745;
    }

    .toast.error {
        background: #dc3545;
    }

    .toast.warning {
        background: #ffc107;
        color: #212529;
    }

    /* 移动端响应式 */
    @media (max-width: 768px) {
        .header-right {
            flex-direction: column;
            align-items: stretch;
            gap: 5px;
        }

        .quick-search-input {
            width: 100%;
            max-width: none;
        }

        .buttons-container {
            justify-content: center;
        }

        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .sortable-table {
            font-size: 10px;
        }

        .sortable-table th,
        .sortable-table td {
            padding: 4px 2px;
        }
    }
        
        .data-table th,
        .data-table td {
            padding: 8px 4px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 采用与抽样检验记录页面相同的简洁布局 -->
<div class="page-header">
    <div class="header-left">
        <h1>待检清单 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}</h1>
    </div>
    <div class="header-right">
        <!-- 搜索框 -->
        <div class="quick-search-form">
            <input type="text" id="quick-search-input" class="quick-search-input" placeholder="请输入料号/名称/供应商进行搜索...">
            <i class="fas fa-search search-icon" id="search-icon"></i>
        </div>

        <!-- 按钮区域 -->
        <div class="buttons-container">
            <a href="{{ url_for('incoming_inspection.batch_import_' + inspection_type) }}" class="btn btn-success">
                <i class="fas fa-upload"></i> 批量导入
            </a>
            {% if inspection_type == 'sampling' %}
            <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增检验
            </a>
            {% else %}
            <a href="{{ url_for('full_inspection.new_inspection') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增检验
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- 简洁的筛选区域 -->
<div class="filter-section">
    <div class="filter-row">
        <div class="filter-group">
            <label for="status-filter">状态:</label>
            <select id="status-filter">
                <option value="">全部</option>
                <option value="pending">待检</option>
                <option value="in_progress">检验中</option>
                <option value="completed">已完成</option>
            </select>
        </div>
        <div class="filter-group">
            <label for="date-from">到货日期:</label>
            <input type="date" id="date-from">
        </div>
        <div class="filter-group">
            <label>至:</label>
            <input type="date" id="date-to">
        </div>
        <div class="filter-group">
            <button type="button" class="btn btn-primary btn-sm" onclick="applyFilters()">
                <i class="fas fa-search"></i> 查询
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="resetFilters()">
                <i class="fas fa-undo"></i> 重置
            </button>
        </div>
    </div>
</div>

<!-- 简洁的表格容器 - 与抽样检验记录一致 -->
<div class="table-container">
    <!-- 加载状态 -->
    <div class="loading" id="loading">
        正在加载数据...
    </div>

    <!-- 数据表格 -->
    <table class="sortable-table" id="data-table" style="display: none;">
        <thead>
            <tr>
                <th data-sort="material_number">料号</th>
                <th data-sort="material_name">名称</th>
                <th data-sort="specification">规格</th>
                <th data-sort="supplier_name">供应商</th>
                <th data-sort="incoming_quantity">数量</th>
                <th data-sort="unit">单位</th>
                <th data-sort="batch_number">批次号</th>
                <th data-sort="arrival_date">到货日期</th>
                <th data-sort="status">状态</th>
                <th data-sort="inspector">检验员</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="data-tbody">
            <!-- 数据将在这里动态生成 -->
        </tbody>
    </table>

    <!-- 空状态 -->
    <div class="empty-state" id="empty-state" style="display: none;">
        <p>暂无待检物料，点击"批量导入"开始添加</p>
    </div>
</div>

<!-- 简洁的分页 -->
<div class="pagination-container" id="pagination-container" style="display: none;">
    <div class="summary" id="summary">显示第 1-20 条，共 0 条记录</div>
    <div class="pagination" id="pagination">
        <!-- 分页按钮将在这里动态生成 -->
    </div>
</div>

<!-- Toast 提示 -->
<div id="toast" class="toast"></div>
{% endblock %}

{% block extra_js %}
<script>
    const inspectionType = '{{ inspection_type }}';
    let currentPage = 1;
    let currentFilters = {};

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadData();
        setupSearch();
    });

    // 设置搜索功能
    function setupSearch() {
        const searchInput = document.getElementById('quick-search-input');
        const searchIcon = document.getElementById('search-icon');

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        searchIcon.addEventListener('click', performSearch);
    }

    function performSearch() {
        const searchTerm = document.getElementById('quick-search-input').value.trim();
        currentFilters.search = searchTerm;
        currentPage = 1;
        loadData();
    }

    async function loadData(page = 1) {
        showLoading();

        try {
            const params = new URLSearchParams({
                type: inspectionType,
                page: page,
                per_page: 20,
                ...currentFilters
            });

            const response = await fetch(`/pending_inspection/api/pending_inspections?${params}`);
            const data = await response.json();

            if (data.success) {
                renderData(data.data);
                updatePagination(data.data);
            } else {
                showToast('加载数据失败: ' + data.error, 'error');
                showEmptyState();
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            showToast('加载数据失败，请重试', 'error');
            showEmptyState();
        } finally {
            hideLoading();
        }
    }

    function renderData(data) {
        const tbody = document.getElementById('data-tbody');

        if (!data.items || data.items.length === 0) {
            showEmptyState();
            return;
        }

        let html = '';
        data.items.forEach(item => {
            html += `
                <tr>
                    <td>${item.material_code || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.specification || ''}</td>
                    <td>${item.supplier_name || ''}</td>
                    <td>${item.incoming_quantity || ''}</td>
                    <td>${item.unit || ''}</td>
                    <td>${item.batch_number || ''}</td>
                    <td>${item.arrival_date || ''}</td>
                    <td>${getStatusBadge(item.status)}</td>
                    <td>${item.inspector || ''}</td>
                    <td>${getActionButtons(item)}</td>
                </tr>
            `;
        });

        tbody.innerHTML = html;
        document.getElementById('data-table').style.display = 'table';
        document.getElementById('empty-state').style.display = 'none';
    }

    function getStatusBadge(status) {
        const statusMap = {
            'pending': { text: '待检', class: 'status-pending' },
            'in_progress': { text: '检验中', class: 'status-progress' },
            'completed': { text: '已完成', class: 'status-completed' },
            'cancelled': { text: '已取消', class: 'status-cancelled' }
        };
        
        const statusInfo = statusMap[status] || { text: status, class: 'status-pending' };
        return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    function getActionButtons(item) {
        let buttons = '';

        if (item.status === 'pending') {
            buttons += `
                <button type="button" class="btn btn-success btn-sm" onclick="startInspection(${item.id})" title="开始检验">
                    开始
                </button>
                <button type="button" class="btn btn-warning btn-sm" onclick="editItem(${item.id})" title="编辑">
                    编辑
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteItem(${item.id})" title="删除">
                    删除
                </button>
            `;
        } else if (item.status === 'in_progress') {
            const editUrl = inspectionType === 'sampling'
                ? `/sampling_inspection/edit/${item.inspection_record_id}`
                : `/full_inspection/edit/${item.inspection_record_id}`;
            buttons += `
                <a href="${editUrl}" class="btn btn-primary btn-sm" title="继续检验">
                    继续
                </a>
            `;
        } else if (item.status === 'completed') {
            const viewUrl = inspectionType === 'sampling'
                ? `/sampling_inspection/view/${item.inspection_record_id}`
                : `/full_inspection/view/${item.inspection_record_id}`;
            buttons += `
                <a href="${viewUrl}" class="btn btn-secondary btn-sm" title="查看记录">
                    查看
                </a>
            `;
        }

        return `<div class="action-buttons">${buttons}</div>`;
    }

    function updatePagination(data) {
        const container = document.getElementById('pagination-container');
        const summary = document.getElementById('summary');
        const pagination = document.getElementById('pagination');

        if (!data.items || data.items.length === 0) {
            container.style.display = 'none';
            return;
        }

        // 更新摘要信息
        const start = (data.page - 1) * data.per_page + 1;
        const end = Math.min(data.page * data.per_page, data.total);
        summary.textContent = `显示第 ${start}-${end} 条，共 ${data.total} 条记录`;

        // 生成分页按钮
        let paginationHtml = '';

        // 上一页
        if (data.page > 1) {
            paginationHtml += `<a href="#" onclick="loadData(${data.page - 1}); return false;">上一页</a>`;
        } else {
            paginationHtml += `<span class="disabled">上一页</span>`;
        }

        // 页码
        for (let i = Math.max(1, data.page - 2); i <= Math.min(data.pages, data.page + 2); i++) {
            if (i === data.page) {
                paginationHtml += `<span class="active">${i}</span>`;
            } else {
                paginationHtml += `<a href="#" onclick="loadData(${i}); return false;">${i}</a>`;
            }
        }

        // 下一页
        if (data.page < data.pages) {
            paginationHtml += `<a href="#" onclick="loadData(${data.page + 1}); return false;">下一页</a>`;
        } else {
            paginationHtml += `<span class="disabled">下一页</span>`;
        }

        pagination.innerHTML = paginationHtml;
        container.style.display = 'flex';
    }

    function applyFilters() {
        currentFilters = {
            status: document.getElementById('status-filter').value,
            date_from: document.getElementById('date-from').value,
            date_to: document.getElementById('date-to').value
        };

        // 移除空值
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });

        currentPage = 1;
        loadData();
    }

    function resetFilters() {
        document.getElementById('status-filter').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';
        document.getElementById('quick-search-input').value = '';

        currentFilters = {};
        currentPage = 1;
        loadData();
    }

    async function startInspection(itemId) {
        if (!confirm('确定要开始检验吗？')) {
            return;
        }

        try {
            const response = await fetch(`/pending_inspection/api/pending_inspections/${itemId}/start_inspection`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                showToast('检验记录创建成功', 'success');
                const editUrl = data.data.inspection_type === 'sampling'
                    ? `/sampling_inspection/edit/${data.data.inspection_record_id}`
                    : `/full_inspection/edit/${data.data.inspection_record_id}`;
                setTimeout(() => window.location.href = editUrl, 1000);
            } else {
                showToast('创建检验记录失败: ' + data.error, 'error');
            }
        } catch (error) {
            showToast('开始检验失败，请重试', 'error');
        }
    }

    function editItem(itemId) {
        showToast('编辑功能开发中', 'warning');
    }

    async function deleteItem(itemId) {
        if (!confirm('确定要删除这个待检物料吗？')) {
            return;
        }

        try {
            const response = await fetch(`/pending_inspection/api/pending_inspections/${itemId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                showToast('删除成功', 'success');
                loadData(currentPage);
            } else {
                showToast('删除失败: ' + data.error, 'error');
            }
        } catch (error) {
            showToast('删除失败，请重试', 'error');
        }
    }

    // 工具函数
    function showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('data-table').style.display = 'none';
        document.getElementById('empty-state').style.display = 'none';
    }

    function hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    function showEmptyState() {
        document.getElementById('data-table').style.display = 'none';
        document.getElementById('empty-state').style.display = 'block';
        document.getElementById('pagination-container').style.display = 'none';
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type} show`;

        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }


</script>
{% endblock %}
