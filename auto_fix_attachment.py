#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修复附件文件名问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config import get_db_connection

def auto_fix_attachment_17():
    """自动修复附件ID 17的文件名问题"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🔧 自动修复附件ID 17的文件名...")
        
        # 获取附件信息
        cursor.execute("""
            SELECT id, material_id, file_name, file_path, file_extension, upload_time
            FROM material_attachments 
            WHERE id = 17
        """)
        
        attachment = cursor.fetchone()
        if not attachment:
            print("❌ 未找到附件ID 17")
            return False
        
        attachment_id, material_id, file_name, file_path, file_extension, upload_time = attachment
        
        print(f"📄 当前信息:")
        print(f"   文件名: '{file_name}'")
        print(f"   文件路径: '{file_path}'")
        print(f"   扩展名: '{file_extension}'")
        print(f"   上传时间: {upload_time}")
        
        # 检查实际文件是否存在
        file_exists = False
        if file_path:
            full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
            if os.path.exists(full_path):
                file_exists = True
                file_size = os.path.getsize(full_path)
                print(f"   实际文件大小: {file_size} 字节")
                
                # 尝试从文件内容判断文件类型
                with open(full_path, 'rb') as f:
                    header = f.read(8)
                    if header.startswith(b'PK\x03\x04'):
                        print("   文件类型: Excel/Office文档 (ZIP格式)")
                    elif header.startswith(b'%PDF'):
                        print("   文件类型: PDF文档")
                    else:
                        print(f"   文件头: {header.hex()}")
            else:
                print("   ❌ 实际文件不存在")
        
        # 生成一个有意义的文件名
        # 根据文件大小和类型，这可能是一个重要的规格文档
        timestamp = upload_time.strftime('%Y%m%d_%H%M%S')
        
        # 根据文件大小判断可能的文件类型
        if file_exists and file_size > 1000000:  # 大于1MB
            new_filename = f"产品规格文档_{timestamp}.xlsx"
        else:
            new_filename = f"相关文档_{timestamp}.xlsx"
        
        print(f"\n🔄 将文件名更新为: '{new_filename}'")
        
        # 执行更新
        cursor.execute("""
            UPDATE material_attachments 
            SET file_name = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (new_filename, attachment_id))
        
        conn.commit()
        
        print("✅ 文件名修复成功！")
        
        # 验证修复结果
        cursor.execute("""
            SELECT file_name FROM material_attachments WHERE id = %s
        """, (attachment_id,))
        
        updated_name = cursor.fetchone()[0]
        print(f"✅ 验证: 新文件名为 '{updated_name}'")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def fix_all_problematic_attachments():
    """修复所有有问题的附件文件名"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("\n🔧 查找并修复所有有问题的附件...")
        
        # 查找所有有问题的附件
        cursor.execute("""
            SELECT id, material_id, file_name, file_path, file_extension, upload_time
            FROM material_attachments 
            WHERE is_active = TRUE 
            AND (
                file_name = file_extension 
                OR file_name = '' 
                OR file_name IS NULL
                OR file_name LIKE '.%'
                OR LENGTH(file_name) < 3
            )
            ORDER BY upload_time DESC
        """)
        
        problematic_attachments = cursor.fetchall()
        
        if not problematic_attachments:
            print("✅ 没有发现其他有问题的附件")
            return True
        
        print(f"🔍 发现 {len(problematic_attachments)} 个有问题的附件")
        
        fixed_count = 0
        
        for attachment in problematic_attachments:
            attachment_id, material_id, file_name, file_path, file_extension, upload_time = attachment
            
            print(f"\n📄 修复附件ID: {attachment_id}")
            print(f"   当前文件名: '{file_name}'")
            print(f"   文件扩展名: '{file_extension}'")
            
            # 尝试从文件路径中提取真实文件名
            new_filename = None
            
            if file_path:
                # 从路径中提取文件名
                path_parts = file_path.replace('\\', '/').split('/')
                if path_parts:
                    stored_filename = path_parts[-1]
                    # 移除UUID前缀
                    if '_' in stored_filename:
                        parts = stored_filename.split('_', 1)
                        if len(parts) == 2 and len(parts[0]) == 32:  # UUID长度
                            original_name = parts[1]
                            # 如果原始名称有意义（不只是扩展名）
                            if original_name != file_extension and len(original_name) > len(file_extension):
                                new_filename = original_name
            
            # 如果无法从路径提取有意义的文件名，生成一个
            if not new_filename:
                timestamp = upload_time.strftime('%Y%m%d_%H%M%S')
                if file_extension:
                    if file_extension.lower() in ['jpg', 'jpeg', 'png', 'gif']:
                        new_filename = f"图片_{timestamp}.{file_extension}"
                    elif file_extension.lower() in ['pdf']:
                        new_filename = f"文档_{timestamp}.{file_extension}"
                    elif file_extension.lower() in ['xlsx', 'xls']:
                        new_filename = f"表格文档_{timestamp}.{file_extension}"
                    elif file_extension.lower() in ['docx', 'doc']:
                        new_filename = f"文档_{timestamp}.{file_extension}"
                    else:
                        new_filename = f"附件_{timestamp}.{file_extension}"
                else:
                    new_filename = f"附件_{timestamp}"
            
            print(f"   新文件名: '{new_filename}'")
            
            # 执行修复
            cursor.execute("""
                UPDATE material_attachments 
                SET file_name = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (new_filename, attachment_id))
            
            fixed_count += 1
            print(f"   ✅ 已修复")
        
        conn.commit()
        print(f"\n🎉 批量修复完成！共修复了 {fixed_count} 个附件")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 批量修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 自动修复附件文件名")
    print("=" * 50)
    
    # 1. 修复特定的附件ID 17
    if auto_fix_attachment_17():
        print("✅ 附件ID 17修复成功")
    
    # 2. 修复所有其他有问题的附件
    if fix_all_problematic_attachments():
        print("✅ 所有附件修复完成")
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！现在所有附件都应该显示正确的文件名了。")
    print("💡 建议刷新浏览器页面查看效果。")

if __name__ == "__main__":
    main()
