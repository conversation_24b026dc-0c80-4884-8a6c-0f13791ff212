# 批量导入待检功能部署指南

## 📋 部署概述

本指南详细说明了如何部署和配置QMS系统的批量导入待检功能。该功能包括数据库表创建、后端API部署、前端页面集成等步骤。

## 🗄️ 数据库部署

### 1. 创建数据库表

执行以下SQL脚本创建必要的数据库表：

```sql
-- 1. 创建待检物料表
CREATE TABLE IF NOT EXISTS pending_inspections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL COMMENT '物料料号',
    material_name VARCHAR(200) COMMENT '物料名称',
    specification VARCHAR(500) COMMENT '规格型号',
    supplier_name VARCHAR(200) COMMENT '供应商名称',
    incoming_quantity DECIMAL(10,3) COMMENT '来料数量',
    unit VARCHAR(20) COMMENT '单位',
    inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    batch_number VARCHAR(100) COMMENT '批次号',
    arrival_date DATE COMMENT '到货日期',
    planned_inspection_date DATE COMMENT '计划检验日期',
    inspector VARCHAR(50) COMMENT '检验员',
    remarks TEXT COMMENT '备注',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    inspection_record_id INT COMMENT '关联的检验记录ID',
    
    INDEX idx_material_code (material_code),
    INDEX idx_inspection_type (inspection_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_arrival_date (arrival_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待检物料表';

-- 2. 创建待检物料批次表
CREATE TABLE IF NOT EXISTS pending_inspection_batches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    batch_name VARCHAR(100) NOT NULL COMMENT '批次名称',
    inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型',
    total_items INT DEFAULT 0 COMMENT '总物料数',
    pending_items INT DEFAULT 0 COMMENT '待检数量',
    in_progress_items INT DEFAULT 0 COMMENT '检验中数量',
    completed_items INT DEFAULT 0 COMMENT '已完成数量',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_inspection_type (inspection_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待检物料批次表';

-- 3. 添加外键约束
ALTER TABLE pending_inspections 
ADD COLUMN batch_id INT COMMENT '批次ID',
ADD INDEX idx_batch_id (batch_id),
ADD FOREIGN KEY (batch_id) REFERENCES pending_inspection_batches(id) ON DELETE SET NULL,
ADD FOREIGN KEY (inspection_record_id) REFERENCES sampling_inspection_records(id) ON DELETE SET NULL;
```

### 2. 验证表创建

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'pending_%';

-- 检查表结构
DESCRIBE pending_inspections;
DESCRIBE pending_inspection_batches;
```

## 🔧 后端部署

### 1. 创建API模块

将以下文件添加到项目中：

```
blueprints/
└── pending_inspection/
    ├── __init__.py
    └── api.py
```

### 2. 注册蓝图

在 `app.py` 中添加蓝图注册：

```python
# 导入部分
from blueprints.pending_inspection.api import pending_inspection_bp

# 注册部分
app.register_blueprint(pending_inspection_bp, url_prefix='/pending_inspection')
```

### 3. 验证API部署

启动应用后，访问以下URL验证API是否正常：

```
GET /pending_inspection/api/pending_inspections?type=sampling
```

## 🎨 前端部署

### 1. 添加页面模板

将以下模板文件添加到项目中：

```
blueprints/incoming_inspection/templates/
├── batch_import_sampling.html
└── pending_list.html
```

### 2. 修改导航栏

在 `templates/base.html` 中添加导航链接：

**抽样检验模块**：
```html
<div class="menu-cell">
    <a href="{{ url_for('incoming_inspection.batch_import_sampling') }}" class="menu-item">
        批量导入待检
    </a>
</div>
```

**全部检验模块**：
```html
<div class="menu-cell">
    <a href="{{ url_for('incoming_inspection.batch_import_full') }}" class="menu-item">
        批量导入待检
    </a>
</div>
```

### 3. 添加路由

在 `blueprints/incoming_inspection/routes.py` 中添加路由：

```python
# 批量导入待检 - 抽样检验
@incoming_inspection_bp.route('/batch_import_sampling')
def batch_import_sampling():
    return render_template('batch_import_sampling.html', inspection_type='sampling')

# 批量导入待检 - 全部检验
@incoming_inspection_bp.route('/batch_import_full')
def batch_import_full():
    return render_template('batch_import_sampling.html', inspection_type='full')

# 待检清单
@incoming_inspection_bp.route('/pending_list')
def pending_list():
    inspection_type = request.args.get('type', 'sampling')
    return render_template('pending_list.html', inspection_type=inspection_type)
```

## 🚀 部署步骤

### 第一步：备份数据库
```sql
-- 创建数据库备份
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：执行数据库脚本
```bash
# 连接数据库
mysql -u username -p database_name

# 执行建表脚本
source database/create_pending_inspection_table.sql
```

### 第三步：部署后端代码
```bash
# 1. 创建目录
mkdir -p blueprints/pending_inspection

# 2. 复制API文件
cp blueprints/pending_inspection/api.py blueprints/pending_inspection/

# 3. 创建__init__.py
touch blueprints/pending_inspection/__init__.py

# 4. 修改app.py
# (手动编辑添加蓝图注册)
```

### 第四步：部署前端代码
```bash
# 1. 复制模板文件
cp blueprints/incoming_inspection/templates/batch_import_sampling.html blueprints/incoming_inspection/templates/
cp blueprints/incoming_inspection/templates/pending_list.html blueprints/incoming_inspection/templates/

# 2. 修改导航栏
# (手动编辑templates/base.html)

# 3. 添加路由
# (手动编辑blueprints/incoming_inspection/routes.py)
```

### 第五步：重启应用
```bash
# 重启Flask应用
sudo systemctl restart qms-app
# 或
python app.py
```

### 第六步：验证部署
1. **访问批量导入页面**
   - 抽样检验：`/incoming_inspection/batch_import_sampling`
   - 全部检验：`/incoming_inspection/batch_import_full`

2. **访问待检清单页面**
   - 抽样检验：`/incoming_inspection/pending_list?type=sampling`
   - 全部检验：`/incoming_inspection/pending_list?type=full`

3. **测试API接口**
   ```bash
   # 测试获取待检清单
   curl -X GET "http://localhost:5000/pending_inspection/api/pending_inspections?type=sampling"
   
   # 测试批量导入
   curl -X POST "http://localhost:5000/pending_inspection/api/pending_inspections/batch_import" \
        -H "Content-Type: application/json" \
        -d '{"inspection_type":"sampling","materials":[{"material_code":"TEST001"}]}'
   ```

## 🔍 功能测试

### 1. 批量导入测试

#### 手动录入测试
1. 访问批量导入页面
2. 选择"手动录入"方式
3. 输入物料料号，验证自动获取信息
4. 添加多行数据
5. 点击"预览数据"
6. 点击"确认导入"
7. 验证跳转到待检清单

#### 文件导入测试
1. 准备Excel测试文件
2. 选择"文件导入"方式
3. 上传测试文件
4. 验证文件解析结果
5. 确认导入

### 2. 待检清单测试

#### 列表功能测试
1. 访问待检清单页面
2. 验证数据正确显示
3. 测试筛选功能
4. 测试分页功能
5. 测试排序功能

#### 操作功能测试
1. 测试"开始检验"功能
2. 验证检验记录创建
3. 测试编辑功能
4. 测试删除功能
5. 验证状态更新

### 3. 集成测试

#### 与检验记录集成
1. 从待检清单开始检验
2. 验证检验记录数据正确
3. 完成检验后验证状态更新
4. 测试检验记录查看

#### 与物料管理集成
1. 验证物料信息自动获取
2. 测试供应商信息获取
3. 验证单位信息正确

## 🐛 故障排除

### 常见问题

#### 1. 数据库连接错误
**症状**：API返回数据库连接失败
**解决**：
```bash
# 检查数据库连接配置
# 检查数据库服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u username -p -e "SELECT 1"
```

#### 2. 表不存在错误
**症状**：查询时提示表不存在
**解决**：
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'pending_%';

-- 重新执行建表脚本
SOURCE database/create_pending_inspection_table.sql;
```

#### 3. 权限错误
**症状**：无法访问页面或API
**解决**：
```python
# 检查路由注册
# 检查蓝图导入
# 检查URL配置
```

#### 4. 前端页面显示异常
**症状**：页面样式错误或功能不正常
**解决**：
```bash
# 清除浏览器缓存
# 检查静态文件路径
# 检查JavaScript错误
```

### 日志查看

#### 应用日志
```bash
# 查看应用日志
tail -f /var/log/qms/app.log

# 查看错误日志
tail -f /var/log/qms/error.log
```

#### 数据库日志
```bash
# 查看MySQL错误日志
tail -f /var/log/mysql/error.log

# 查看慢查询日志
tail -f /var/log/mysql/slow.log
```

## 📊 性能优化

### 1. 数据库优化
```sql
-- 添加必要索引
CREATE INDEX idx_pending_material_status ON pending_inspections(material_code, status);
CREATE INDEX idx_pending_created_date ON pending_inspections(created_at);

-- 分析表性能
ANALYZE TABLE pending_inspections;
ANALYZE TABLE pending_inspection_batches;
```

### 2. 查询优化
```python
# 使用分页查询
# 添加查询缓存
# 优化JOIN查询
```

### 3. 前端优化
```javascript
// 使用防抖处理搜索
// 实现虚拟滚动
// 优化DOM操作
```

## 🔒 安全配置

### 1. 数据验证
- 输入数据验证
- SQL注入防护
- XSS攻击防护

### 2. 权限控制
- 用户权限验证
- 操作权限检查
- 数据访问控制

### 3. 日志审计
- 操作日志记录
- 错误日志监控
- 安全事件追踪

## 📈 监控和维护

### 1. 性能监控
- 响应时间监控
- 数据库性能监控
- 系统资源监控

### 2. 数据维护
- 定期数据清理
- 数据备份策略
- 数据完整性检查

### 3. 功能维护
- 定期功能测试
- 用户反馈处理
- 功能优化升级

---

**部署完成后，批量导入待检功能将为QMS系统提供更高效的检验准备流程！**
