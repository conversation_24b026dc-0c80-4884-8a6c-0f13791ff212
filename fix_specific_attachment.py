#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特定附件的文件名问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config import get_db_connection

def fix_attachment_17():
    """修复附件ID 17的文件名问题"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🔧 修复附件ID 17的文件名...")
        
        # 获取附件信息
        cursor.execute("""
            SELECT id, material_id, file_name, file_path, file_extension, upload_time
            FROM material_attachments 
            WHERE id = 17
        """)
        
        attachment = cursor.fetchone()
        if not attachment:
            print("❌ 未找到附件ID 17")
            return False
        
        attachment_id, material_id, file_name, file_path, file_extension, upload_time = attachment
        
        print(f"📄 当前信息:")
        print(f"   文件名: '{file_name}'")
        print(f"   文件路径: '{file_path}'")
        print(f"   扩展名: '{file_extension}'")
        print(f"   上传时间: {upload_time}")
        
        # 检查实际文件是否存在
        if file_path:
            full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f"   实际文件大小: {file_size} 字节")
                
                # 尝试从文件内容判断文件类型
                with open(full_path, 'rb') as f:
                    header = f.read(8)
                    if header.startswith(b'PK\x03\x04'):
                        print("   文件类型: Excel/Office文档 (ZIP格式)")
                    elif header.startswith(b'%PDF'):
                        print("   文件类型: PDF文档")
                    else:
                        print(f"   文件头: {header.hex()}")
            else:
                print("   ❌ 实际文件不存在")
        
        # 生成一个有意义的文件名
        # 由于这是最近上传的xlsx文件，我们可以给它一个描述性的名称
        timestamp = upload_time.strftime('%Y%m%d_%H%M%S')
        new_filename = f"相关文档_{timestamp}.xlsx"
        
        print(f"\n💡 建议的新文件名: '{new_filename}'")
        
        # 询问用户是否要使用自定义文件名
        custom_name = input("请输入自定义文件名（包含扩展名），或按回车使用建议名称: ").strip()
        
        if custom_name:
            # 验证自定义文件名
            if not custom_name.endswith('.xlsx'):
                custom_name += '.xlsx'
            new_filename = custom_name
        
        print(f"🔄 将文件名更新为: '{new_filename}'")
        
        # 执行更新
        cursor.execute("""
            UPDATE material_attachments 
            SET file_name = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (new_filename, attachment_id))
        
        conn.commit()
        
        print("✅ 文件名修复成功！")
        
        # 验证修复结果
        cursor.execute("""
            SELECT file_name FROM material_attachments WHERE id = %s
        """, (attachment_id,))
        
        updated_name = cursor.fetchone()[0]
        print(f"✅ 验证: 新文件名为 '{updated_name}'")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 修复特定附件文件名")
    print("=" * 50)
    
    if fix_attachment_17():
        print("\n🎉 修复完成！现在附件应该显示正确的文件名了。")
        print("💡 建议刷新浏览器页面查看效果。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")

if __name__ == "__main__":
    main()
