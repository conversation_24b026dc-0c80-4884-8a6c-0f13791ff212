#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查应用状态
"""

import urllib.request
import json

def check_api_status():
    """检查API状态"""
    try:
        url = "http://localhost:5000/pending_inspection/api/pending_inspections?type=sampling"
        
        with urllib.request.urlopen(url, timeout=5) as response:
            data = json.loads(response.read().decode())
            
        print("✅ API状态正常")
        print(f"   响应成功: {data.get('success', False)}")
        if data.get('success'):
            total = data.get('data', {}).get('total', 0)
            print(f"   当前待检物料数量: {total}")
        
        return True
        
    except Exception as e:
        print(f"❌ API状态异常: {e}")
        return False

def check_app_status():
    """检查应用状态"""
    try:
        url = "http://localhost:5000"
        
        with urllib.request.urlopen(url, timeout=5) as response:
            status_code = response.getcode()
            
        if status_code == 200:
            print("✅ 应用状态正常")
            return True
        else:
            print(f"❌ 应用状态异常: HTTP {status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 应用无法访问: {e}")
        return False

if __name__ == "__main__":
    print("=== 应用状态检查 ===")
    
    print("\n1. 检查应用主页...")
    app_ok = check_app_status()
    
    print("\n2. 检查API接口...")
    api_ok = check_api_status()
    
    print("\n=== 检查结果 ===")
    if app_ok and api_ok:
        print("🎉 应用运行正常，URL修复成功！")
        print("\n可以访问的页面:")
        print("- 主页: http://localhost:5000")
        print("- 抽样检验批量导入: http://localhost:5000/incoming_inspection/batch_import_sampling")
        print("- 全部检验批量导入: http://localhost:5000/incoming_inspection/batch_import_full")
        print("- 抽样检验待检清单: http://localhost:5000/incoming_inspection/pending_list?type=sampling")
        print("- 全部检验待检清单: http://localhost:5000/incoming_inspection/pending_list?type=full")
    else:
        print("❌ 应用存在问题，需要进一步检查。")
