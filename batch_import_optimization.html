<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量导入页面优化对比 - 品质中心管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1976d2;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 15px;
        }
        .side-by-side {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .mockup {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .mockup-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3748;
            text-align: center;
            padding: 10px;
            border-radius: 4px;
        }
        .before {
            background: #fff5f5;
            color: #dc3545;
        }
        .after {
            background: #f0fff4;
            color: #28a745;
        }
        
        /* 模拟原版本的复杂样式 */
        .old-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }
        .old-header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
            font-weight: 600;
        }
        .old-import-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .old-import-method {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
        }
        .old-import-method.active {
            border-color: #2196f3;
            background: #e3f2fd;
        }
        .old-import-method i {
            font-size: 2em;
            color: #2196f3;
            margin-bottom: 10px;
        }
        .old-import-method h3 {
            margin: 10px 0 5px 0;
            color: #333;
            font-size: 16px;
        }
        .old-import-method p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        /* 模拟新版本的简洁样式 */
        .new-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .new-header h1 {
            font-size: 18px;
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        .new-import-methods {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .new-import-method {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .new-import-method.active {
            border-color: #1976d2;
            background: #1976d2;
            color: white;
        }
        .new-import-method i {
            font-size: 12px;
        }
        
        .btn {
            padding: 2px 5px;
            font-size: 11px;
            border: 1px solid;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary {
            background: #1976d2;
            color: white;
            border-color: #1976d2;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }
        
        .table-demo {
            font-size: 11px;
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .table-demo th, .table-demo td {
            padding: 3px 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
            vertical-align: middle;
        }
        .table-demo th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        .table-demo input {
            width: 100%;
            padding: 2px 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 11px;
            height: 24px;
            box-sizing: border-box;
        }
        
        .highlight-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        
        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .improvement-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .improvement-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .improvement-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2d3748;
        }
        .improvement-desc {
            font-size: 14px;
            color: #4a5568;
        }
        
        @media (max-width: 768px) {
            .side-by-side {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📤 批量导入页面优化对比</h1>
        <p>采用与待检清单相同的简洁风格，优化批量导入页面的用户体验</p>

        <div class="comparison-section">
            <div class="section-title">🎨 页面头部对比</div>
            <div class="side-by-side">
                <div class="mockup">
                    <div class="mockup-title before">优化前：复杂的头部设计</div>
                    <div class="old-header">
                        <h1><i class="fas fa-upload"></i> 批量导入待检 - 抽样检验</h1>
                        <div style="display: flex; gap: 10px;">
                            <a href="#" style="padding: 8px 16px; font-size: 14px; background: #6c757d; color: white; text-decoration: none; border-radius: 4px;">
                                <i class="fas fa-list"></i> 待检清单
                            </a>
                            <a href="#" style="padding: 8px 16px; font-size: 14px; background: #2196f3; color: white; text-decoration: none; border-radius: 4px;">
                                <i class="fas fa-plus"></i> 新增检验
                            </a>
                        </div>
                    </div>
                    <p style="color: #dc3545; font-size: 12px;">❌ 字体过大、间距过宽、视觉沉重</p>
                </div>
                
                <div class="mockup">
                    <div class="mockup-title after">优化后：简洁的头部设计</div>
                    <div class="new-header">
                        <h1>批量导入待检 - 抽样检验</h1>
                        <div style="display: flex; gap: 8px;">
                            <a href="#" class="btn btn-secondary">
                                <i class="fas fa-list"></i> 待检清单
                            </a>
                            <a href="#" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 新增检验
                            </a>
                        </div>
                    </div>
                    <p style="color: #28a745; font-size: 12px;">✅ 与抽样检验记录一致的简洁风格</p>
                </div>
            </div>
        </div>

        <div class="comparison-section">
            <div class="section-title">🔄 导入方式选择对比</div>
            <div class="side-by-side">
                <div class="mockup">
                    <div class="mockup-title before">优化前：卡片式设计</div>
                    <h2 style="margin-bottom: 20px;">选择导入方式</h2>
                    <div class="old-import-methods">
                        <div class="old-import-method active">
                            <i class="fas fa-keyboard"></i>
                            <h3>手动录入</h3>
                            <p>逐行输入物料信息，适合少量物料</p>
                        </div>
                        <div class="old-import-method">
                            <i class="fas fa-file-excel"></i>
                            <h3>文件导入</h3>
                            <p>上传Excel文件批量导入，适合大量物料</p>
                        </div>
                    </div>
                    <p style="color: #dc3545; font-size: 12px;">❌ 占用空间大、视觉复杂</p>
                </div>
                
                <div class="mockup">
                    <div class="mockup-title after">优化后：标签式设计</div>
                    <div class="new-import-methods">
                        <div class="new-import-method active">
                            <i class="fas fa-keyboard"></i> 手动录入
                        </div>
                        <div class="new-import-method">
                            <i class="fas fa-file-excel"></i> 文件导入
                        </div>
                    </div>
                    <p style="color: #28a745; font-size: 12px;">✅ 紧凑简洁、一目了然</p>
                </div>
            </div>
        </div>

        <div class="comparison-section">
            <div class="section-title">📋 表格样式对比</div>
            <div class="side-by-side">
                <div class="mockup">
                    <div class="mockup-title before">优化前：大尺寸表格</div>
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <thead>
                            <tr>
                                <th style="padding: 10px; border: 1px solid #ddd; background: #f8f9fa;">料号</th>
                                <th style="padding: 10px; border: 1px solid #ddd; background: #f8f9fa;">名称</th>
                                <th style="padding: 10px; border: 1px solid #ddd; background: #f8f9fa;">数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">
                                    <input type="text" style="width: 100%; padding: 6px 8px; font-size: 14px;">
                                </td>
                                <td style="padding: 10px; border: 1px solid #ddd;">
                                    <input type="text" style="width: 100%; padding: 6px 8px; font-size: 14px;">
                                </td>
                                <td style="padding: 10px; border: 1px solid #ddd;">
                                    <input type="number" style="width: 100%; padding: 6px 8px; font-size: 14px;">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p style="color: #dc3545; font-size: 12px;">❌ 字体大、间距宽、占用空间多</p>
                </div>
                
                <div class="mockup">
                    <div class="mockup-title after">优化后：紧凑表格</div>
                    <table class="table-demo">
                        <thead>
                            <tr>
                                <th>料号</th>
                                <th>名称</th>
                                <th>数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" placeholder="料号"></td>
                                <td><input type="text" placeholder="名称"></td>
                                <td><input type="number" placeholder="数量"></td>
                            </tr>
                        </tbody>
                    </table>
                    <p style="color: #28a745; font-size: 12px;">✅ 与抽样检验记录一致的紧凑风格</p>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 优化策略</h3>
            <p>采用与待检清单和抽样检验记录相同的设计语言，确保整个系统的一致性：</p>
            <ul>
                <li><strong>统一字体大小：</strong>页面标题18px，按钮和表格11px</li>
                <li><strong>统一间距规范：</strong>页面头部margin-bottom: 10px</li>
                <li><strong>统一按钮样式：</strong>padding: 2px 5px, font-size: 11px</li>
                <li><strong>统一表格风格：</strong>紧凑的单元格间距和字体大小</li>
                <li><strong>简化视觉元素：</strong>移除不必要的装饰和复杂布局</li>
            </ul>
        </div>

        <div class="improvement-list">
            <div class="improvement-card">
                <div class="improvement-icon">🎨</div>
                <div class="improvement-title">视觉一致性</div>
                <div class="improvement-desc">与系统其他页面保持完全一致的设计风格</div>
            </div>
            <div class="improvement-card">
                <div class="improvement-icon">📱</div>
                <div class="improvement-title">空间利用</div>
                <div class="improvement-desc">紧凑的布局设计，提高屏幕空间利用率</div>
            </div>
            <div class="improvement-card">
                <div class="improvement-icon">⚡</div>
                <div class="improvement-title">操作效率</div>
                <div class="improvement-desc">简化的界面减少视觉干扰，提升操作效率</div>
            </div>
            <div class="improvement-card">
                <div class="improvement-icon">🔧</div>
                <div class="improvement-title">维护便利</div>
                <div class="improvement-desc">统一的样式规范，降低维护成本</div>
            </div>
        </div>

        <div class="comparison-section">
            <div class="section-title">✅ 优化成果</div>
            <h4>技术改进：</h4>
            <ul>
                <li><strong>CSS简化：</strong>移除复杂的卡片样式和大尺寸设计</li>
                <li><strong>HTML精简：</strong>简化结构，移除不必要的包装元素</li>
                <li><strong>样式统一：</strong>采用与其他页面相同的CSS类和规范</li>
                <li><strong>响应式优化：</strong>更好的移动端适配</li>
            </ul>
            
            <h4>用户体验提升：</h4>
            <ul>
                <li><strong>学习成本降低：</strong>与其他页面操作方式一致</li>
                <li><strong>视觉负担减轻：</strong>简洁的界面减少认知负荷</li>
                <li><strong>操作效率提升：</strong>紧凑的布局提高信息密度</li>
                <li><strong>系统一致性：</strong>整个系统具有统一的视觉语言</li>
            </ul>
        </div>

        <div class="highlight-box">
            <h3>🚀 批量导入页面优化完成</h3>
            <p><strong>现在批量导入页面具有与待检清单和抽样检验记录完全一致的简洁风格！</strong></p>
            <p>主要改进包括：</p>
            <ul>
                <li>✅ 统一的页面头部设计和间距</li>
                <li>✅ 简洁的导入方式选择界面</li>
                <li>✅ 紧凑的表格样式和字体大小</li>
                <li>✅ 一致的按钮样式和操作体验</li>
                <li>✅ 优化的文件上传和预览区域</li>
            </ul>
        </div>
    </div>
</body>
</html>
