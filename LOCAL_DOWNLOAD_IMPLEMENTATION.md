# 局域网用户本地下载功能实现

## 🎯 功能需求

局域网用户需要能够：
1. **自动下载文件**到指定目录（默认C:\QMS1）
2. **下载完成后自动打开**文件
3. **显示下载进度条**
4. **支持自定义下载目录**位置

## ✅ 实现方案

### 1. 恢复服务器端下载逻辑

**修改文件**: `blueprints/incoming_inspection/templates/new_sampling_inspection.html`

**核心逻辑**:
```javascript
function openAttachment(fileName, url, fileExtension) {
    const extension = fileExtension.toLowerCase();
    const inlineTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'txt', 'html', 'htm', 'xml', 'json'];

    if (inlineTypes.includes(extension)) {
        // 可预览文件：浏览器中打开
        window.open(url, '_blank', 'noopener,noreferrer');
    } else {
        // 其他文件：下载到本地并自动打开
        downloadAndOpenFile(url, fileName, extension);
    }
}
```

### 2. 下载流程实现

#### 步骤1: 获取下载路径
```javascript
function getDownloadPathAndDownload(sourceUrl, fileName, fileExtension) {
    fetch('/system_settings/api/get_download_path')
    .then(response => response.json())
    .then(data => {
        const localPath = data.success ? data.download_path : 'C:\\QMS1\\';
        downloadToLocalPath(sourceUrl, fileName, fileExtension, localPath);
    });
}
```

#### 步骤2: 下载到本地
```javascript
function downloadToLocalPath(sourceUrl, fileName, fileExtension, localPath) {
    // 解析URL获取material_id和attachment_id
    const urlParts = sourceUrl.split('/').filter(part => part !== '');
    const materialIndex = urlParts.indexOf('material');
    const attachmentsIndex = urlParts.indexOf('attachments');
    const materialId = urlParts[materialIndex + 1];
    const attachmentId = urlParts[attachmentsIndex + 1];

    // 调用下载API
    fetch(`/material_management/api/material/${materialId}/attachments/${attachmentId}/download_to_local`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ local_path: localPath })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 2秒后自动打开文件
            setTimeout(() => {
                autoOpenLocalFile(data.local_file_path, fileName, fileExtension);
            }, 2000);
        }
    });
}
```

#### 步骤3: 自动打开文件
```javascript
function autoOpenLocalFile(filePath, fileName, fileExtension) {
    fetch('/sampling_inspection/api/open_local_file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ file_path: filePath })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`文件 "${fileName}" 已打开`, 'success');
        }
    });
}
```

### 3. 下载路径设置功能

#### 页面头部添加设置按钮
```html
<div class="page-header">
    <h1>新增抽样检验记录</h1>
    <div class="page-header-actions">
        <div class="download-path-display" id="download-path-display">
            下载路径: C:\QMS1\
        </div>
        <button type="button" class="settings-btn" onclick="showDownloadPathSettings()">
            <i class="fas fa-cog"></i>
            设置下载路径
        </button>
    </div>
</div>
```

#### 设置对话框
```html
<div class="path-settings-overlay" id="path-settings-overlay">
    <div class="path-settings-dialog">
        <div class="path-settings-title">
            <i class="fas fa-folder-open"></i>
            设置文件下载路径
        </div>
        <div class="path-input-group">
            <label class="path-input-label">下载路径:</label>
            <input type="text" class="path-input" id="download-path-input" placeholder="例如: C:\QMS1\" />
        </div>
        <div class="path-buttons">
            <button type="button" class="path-btn path-btn-secondary" onclick="closeDownloadPathSettings()">取消</button>
            <button type="button" class="path-btn path-btn-primary" onclick="saveDownloadPath()">保存设置</button>
        </div>
    </div>
</div>
```

### 4. 进度条显示

#### 下载进度对话框
```javascript
function showDownloadProgressDialog(fileName, fileExtension) {
    const overlay = document.createElement('div');
    overlay.className = 'download-progress-overlay';
    overlay.innerHTML = `
        <div class="download-progress-dialog">
            <div class="download-progress-title">
                <i class="fas fa-download"></i>
                正在下载文件
            </div>
            <div class="download-progress-file">${fileName}</div>
            <div class="download-progress-bar">
                <div class="download-progress-fill" id="download-progress-fill"></div>
            </div>
            <div class="download-progress-text" id="download-progress-text">准备下载...</div>
            <div class="download-progress-status" id="download-progress-status">
                <i class="fas fa-spinner fa-spin"></i> 正在处理...
            </div>
        </div>
    `;
    document.body.appendChild(overlay);
}
```

## 🔧 后端API支持

### 1. 系统设置API

**获取下载路径**: `GET /system_settings/api/get_download_path`
```json
{
    "success": true,
    "download_path": "C:\\QMS1\\"
}
```

**设置下载路径**: `POST /system_settings/api/set_download_path`
```json
{
    "download_path": "C:\\QMS1\\新路径\\"
}
```

### 2. 文件下载API

**下载到本地**: `POST /material_management/api/material/{material_id}/attachments/{attachment_id}/download_to_local`
```json
{
    "local_path": "C:\\QMS1\\"
}
```

**响应**:
```json
{
    "success": true,
    "message": "文件下载成功",
    "local_file_path": "C:\\QMS1\\产品规格表.xlsx",
    "file_name": "产品规格表.xlsx",
    "file_size": 2048576
}
```

### 3. 文件打开API

**打开本地文件**: `POST /sampling_inspection/api/open_local_file`
```json
{
    "file_path": "C:\\QMS1\\产品规格表.xlsx"
}
```

## 🎨 用户体验流程

### 完整流程
1. **用户点击"打开"** → 显示下载进度对话框
2. **获取下载路径** → 从系统设置获取或使用默认路径
3. **下载文件** → 显示进度条，文件复制到本地
4. **下载完成** → 进度条显示100%，状态变为"下载完成"
5. **自动打开** → 2秒后调用系统默认程序打开文件
6. **关闭对话框** → 显示成功提示

### 文件类型处理
- **可预览文件** (PDF、图片): 浏览器中直接预览
- **Office文档** (Excel、Word、PPT): 下载到本地并用Office打开
- **CAD图纸** (DWG、DXF): 下载到本地并用CAD软件打开
- **其他文件**: 下载到本地并用默认程序打开

## 🧪 测试验证

### 测试文件
创建了 `test_local_download.html` 用于测试所有功能

### 测试场景
1. **下载路径设置**: 测试获取和设置下载路径
2. **文件下载**: 测试不同类型文件的下载
3. **自动打开**: 测试下载后自动打开功能
4. **进度显示**: 测试下载进度条显示
5. **错误处理**: 测试各种错误情况

## 🔒 安全考虑

### 路径安全
- 验证下载路径的有效性
- 防止路径遍历攻击
- 确保目录创建权限

### 文件安全
- 验证文件存在性
- 检查文件权限
- 防止恶意文件执行

## 📱 兼容性

### 操作系统支持
- **Windows**: 使用 `os.startfile()`
- **macOS**: 使用 `open` 命令
- **Linux**: 使用 `xdg-open` 命令

### 浏览器支持
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 🚀 部署说明

### 立即生效
修改已完成，重启Flask应用后即可使用：
1. 重启Flask应用
2. 清除浏览器缓存
3. 测试附件下载功能

### 配置要求
- 确保下载目录有写入权限
- 确保相关软件已安装（Office、CAD等）
- 确保防火墙允许文件访问

---

**实现完成时间**: 2025-01-27  
**功能范围**: 局域网用户本地文件下载和自动打开  
**兼容性**: 保持与现有功能的完全兼容
