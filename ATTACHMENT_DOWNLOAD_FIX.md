# 来料检验附件下载问题修复指南

## 🔍 问题描述

在来料检验的新增检验页面，输入料号后显示附件，但点击打开时出现以下错误：

1. **系统设置API错误**: `GET "http://192.168.50.203:5000/system_settings/api/get_download_path"`
2. **下载API 404错误**: `POST http://192.168.50.203:5000/sampling_inspection/api/download_to_local 404 (NOT FOUND)`
3. **文件下载失败**: "源文件不存在"

## 🛠️ 根本原因分析

### 1. API路径不匹配
- 来料检验页面调用了不存在的 `/sampling_inspection/api/download_to_local` API
- 实际的下载API在物料管理模块中

### 2. URL解析逻辑问题
- 附件URL格式与下载API期望的参数不匹配
- 缺少健壮的URL解析和错误处理

### 3. 文件路径处理问题
- 相对路径和绝对路径处理不一致
- 缺少路径标准化处理

## ✅ 已实施的修复

### 1. 修复来料检验页面的下载逻辑

**文件**: `blueprints/incoming_inspection/templates/new_sampling_inspection.html`

**修复内容**:
- 修正了下载API调用路径
- 改进了URL解析逻辑
- 添加了错误处理和调试信息

```javascript
// 修复前
fetch('/sampling_inspection/api/download_to_local', {
    // ...
    body: JSON.stringify({
        source_file_path: sourceUrl.replace('/incoming_inspection/api/attachments/', '').replace('/download', ''),
        local_path: localPath
    })
})

// 修复后
const urlParts = sourceUrl.split('/').filter(part => part !== '');
const materialIndex = urlParts.indexOf('material');
const attachmentsIndex = urlParts.indexOf('attachments');
const materialId = urlParts[materialIndex + 1];
const attachmentId = urlParts[attachmentsIndex + 1];

fetch(`/material_management/api/material/${materialId}/attachments/${attachmentId}/download_to_local`, {
    // ...
    body: JSON.stringify({
        local_path: localPath
    })
})
```

### 2. 改进物料管理API的文件路径处理

**文件**: `blueprints/material_management/api.py`

**修复内容**:
- 改进了文件路径的构建逻辑
- 添加了路径标准化处理
- 增强了错误信息

```python
# 构建完整的源文件路径
if os.path.isabs(source_file_path):
    full_source_path = source_file_path
else:
    # 如果是相对路径，构建绝对路径
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    full_source_path = os.path.join(base_dir, source_file_path)

# 标准化路径
full_source_path = os.path.normpath(full_source_path)
```

### 3. 增强错误处理和调试信息

- 添加了URL解析验证
- 增加了详细的错误信息
- 添加了调试日志输出

## 🔧 验证步骤

### 1. 重启Flask应用
```bash
python app.py
```

### 2. 测试系统设置API
访问: `http://192.168.50.203:5000/system_settings/api/get_download_path`

### 3. 测试附件功能
1. 进入来料检验 -> 新增检验页面
2. 输入有附件的物料料号
3. 点击附件的"打开"按钮
4. 检查浏览器控制台是否有错误

### 4. 运行测试脚本
```bash
python test_attachment_download.py
```

## 📋 可能的额外问题和解决方案

### 1. 文件权限问题
如果仍然出现"源文件不存在"错误：

```bash
# 检查文件权限
ls -la static/uploads/material_attachments/

# 修复权限（Linux/Mac）
chmod -R 755 static/uploads/

# Windows下检查文件夹权限
```

### 2. 路径分隔符问题
确保在Windows环境下路径分隔符正确：

```python
# 在config.py中确保路径格式正确
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
```

### 3. 数据库中的文件路径
检查数据库中存储的文件路径是否正确：

```sql
SELECT id, file_name, file_path FROM material_attachments WHERE is_active = TRUE LIMIT 5;
```

## 🚨 注意事项

1. **备份数据**: 在修改前建议备份数据库和文件
2. **测试环境**: 先在测试环境验证修复效果
3. **浏览器缓存**: 清除浏览器缓存以确保加载最新代码
4. **日志监控**: 查看Flask应用日志以获取详细错误信息

## 📞 故障排除

如果问题仍然存在：

1. **检查Flask应用日志**
2. **验证数据库连接**
3. **确认文件系统权限**
4. **检查防火墙设置**
5. **验证网络连接**

## 🎯 预期结果

修复后应该能够：
- ✅ 正常显示物料附件列表
- ✅ 成功下载附件到本地
- ✅ 自动打开下载的文件
- ✅ 显示正确的错误信息（如果有问题）

---

**修复完成时间**: 2025-01-27
**修复版本**: v1.1.0
